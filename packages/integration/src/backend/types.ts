/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2025 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

export type GHGFormData = {
  stationary: {
    fuelRows: StationaryRow[]
    biomassRows: StationaryRow[]
  }
  mobile: MobileRow[]
  fugitive: FugitiveRow[]
  agricultural: {
    animalRows: AnimalRow[]
    fertiliserRows: FertiliserRow[]
  }
  electricity: ElectricityRow[]
  district: DistrictEnergyRow[]
  metadata?: {
    lastSaved?: string
    version?: string
  }
  calculatedTotals?: {
    sectionTotals: Record<string, number>
    scope1Total: number
    scope2Total: number
    scope1AndScope2TotalEmissions: number
  }
}
export interface Field {
  id: string
  label: string
  type: "select" | "number" | "text" | "checkbox"
  options?: string[]
  unit_options?: string[]
}

export interface Section {
  id: string
  title: string
  fields: Field[]
}

export interface Schema {
  sections: Section[]
}

// Row types for forms
export type StationaryRow = {
  fuel: string
  consumption: string
  unit: string
  area: string
  areaUnit: string
}

export type FugitiveRow = {
  name: string
  amount?: string
  unit: string
}

export type MobileRow = {
  category: string
  consumption?: string
  unit?: string
  distance?: string
  distanceUnit?: string
  expense?: string
  fuelType?: string
}

export type AnimalRow = {
  animal: string
  count: string
  fieldPercentage: string
}

export type FertiliserRow = {
  type: string
  amount: string
  unit: string
}

export type ElectricityRow = {
  type: string
  consumption: string
  unit: string
  cost: string
  hasPPA: string
}

export type DistrictEnergyRow = {
  category: string
  consumption: string
  unit: string
  area: string
  areaUnit: string
}

export type GHGSaveResponse = {
  success: boolean
  message: string
  data: {
    created: Array<{
      scope: string
      value: number
      id: number
    }>
    updated: Array<unknown>
    skipped: Array<unknown>
  }
  summary: {
    companyId: number
    year: number
    total_records: number
  }
}

export type YearlyEmissionData = {
  year: number
  scope1Total: number
  scope2Total: number
  scope1and2TotalEmissions: number
}

export type GHGYearsResponse = {
  success: boolean
  data: YearlyEmissionData[]
  message?: string
}

export type GHGYearData = {
  mobile: Record<string, unknown>[]
  stationary: {
    fuelRows: Record<string, unknown>[]
    biomassRows: Record<string, unknown>[]
  }
  fugitive: Record<string, unknown>[]
  agricultural: {
    animalRows: Record<string, unknown>[]
    fertiliserRows: Record<string, unknown>[]
  }
  electricity: Record<string, unknown>[]
  district: Record<string, unknown>[]
  calculatedTotals: {
    scope1Total: number
    scope2Total: number
    sectionTotals: Record<string, number>
    scope1AndScope2TotalEmissions: number
  }
  metadata?: {
    lastSaved: string
    version: string
  }
}

export type GHGYearResponse = {
  success: boolean
  data: GHGYearData
  message: string
}

export type GHGDataResponse = {
  success: boolean
  data: GHGFormData | null
  message?: string
}

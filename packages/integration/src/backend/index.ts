/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import { env } from "../../env"
import { BackendIntegrationService } from "./service"

export const service = new BackendIntegrationService({
  apiUrl: env.BACKEND_API_URL,
  apiToken: env.BACKEND_API_TOKEN,
})

// Export the service and types for use in other parts of the application
export { BackendIntegrationService } from "./service"
export * from "./gql/graphql"
export * from "./types"

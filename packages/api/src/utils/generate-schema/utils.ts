/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import type {
  ActivitySelectField,
  BooleanRadioField,
  CheckboxField,
  DateField,
  Dependency,
  FieldType,
  FileField,
  FormField,
  MultiSelectField,
  NumberField,
  RadioSelectField,
  SchemaField,
  SelectField,
  TableField,
  TextareaField,
  TextField,
} from "./types"

// Determines the field type based on the schema field properties
export function getFieldType(field: SchemaField): FieldType {
  switch (field["x-display"]) {
    case "file-upload":
      return "file"
    case "textarea":
      return "textarea"
    case "multiselect":
      return "multiselect"
    case "radio":
      return "radio"
    case "boolean-radio":
      return "boolean-radio"
    case "activity-selector":
      return "activity-selector"
    case "select":
      return "select"
    case "checkbox":
      return "checkbox"
  }

  if (field.format === "date") return "date"
  switch (field.type) {
    case "string":
      return "text"
    case "integer":
      return "number"
    case "number":
      return "number"
    case "boolean":
      return "checkbox"
  }
  return "text"
}

// Creates a form field based on the schema field, question number, field key, and required status
export function createFormField(
  field: SchemaField,
  questionNumber: string,
  fieldKey: string,
  isRequired: boolean
): FormField {
  // Common properties for all form fields
  const base = {
    questionNumber,
    field: fieldKey,
    title: field.title,
    description: field.description,
    placeholder: field.placeholder,
    helpText: field.helpText,
    isYearField: field.isYearField,
    required: isRequired,
    ...(field.helpText ? { helpText: field.helpText } : {}),
    ...(field.dependency
      ? {
          dependency: normalizeDependencyPaths(field.dependency, fieldKey),
        }
      : {}),
  }

  const fieldType = getFieldType(field)

  switch (fieldType) {
    case "date":
      return { ...base, type: "date", format: field.format } as DateField
    case "select":
      return { ...base, type: "select", enum: field.selectOptions ? field.selectOptions : [] } as SelectField
    case "radio":
      return { ...base, type: "radio", enum: field.selectOptions ? field.selectOptions : [] } as RadioSelectField
    case "number":
      return {
        ...base,
        type: "number",
        minimum: field.minimum,
        maximum: field.maximum,
        isYearField: field["x-display"] === "year",
      } as NumberField
    case "checkbox":
      return { ...base, type: "checkbox" } as CheckboxField
    case "file":
      return { ...base, type: "file" } as FileField
    case "textarea":
      return { ...base, type: "textarea" } as TextareaField
    case "boolean-radio":
      return { ...base, type: "boolean-radio" } as BooleanRadioField
    case "text":
      return { ...base, type: "text" } as TextField
    case "multiselect":
      return {
        ...base,
        type: "multiselect",
        enum: field.items?.selectOptions ?? field.selectOptions ?? [],
      } as MultiSelectField
    case "activity-selector":
      return {
        ...base,
        type: "activity-selector",
        enum: field.selectOptions ?? [],
      } as ActivitySelectField
    default:
      return { ...base, type: "text" } as TextField
  }
}

// Handles the creation of a yearly-table field
export function handleYearlyTable(
  schemaField: SchemaField,
  questionNumber: string,
  fullFieldKey: string,
  isRequired: boolean,
  prefix: string,
  fieldKey: string
): TableField {
  const yearColumns = schemaField["x-year-columns"]!
  const yearFields = yearColumns.map((year: string) => `year_${year}`)

  const rows: {
    key: string
    title?: { en: string; et: string }
    description?: { en: string; et: string }
    questionNumber: string
    isHeader?: boolean
  }[] = []
  let subCounter = 0

  Object.entries(schemaField.properties!).forEach(([scopeKey, scopeValue]) => {
    subCounter++
    const subQuestionNumber = `${questionNumber}.${subCounter}`
    if (scopeValue.type === "object" && scopeValue.properties) {
      if (scopeKey.endsWith("scope_3")) {
        rows.push({
          key: scopeKey,
          title: scopeValue.title,
          description: scopeValue.description,
          questionNumber: subQuestionNumber,
          isHeader: true,
        })

        let subSubCounter = 0
        Object.entries(scopeValue.properties).forEach(([subKey, subValue]) => {
          subSubCounter++
          const subSubQuestionNumber = `${questionNumber}.${subCounter}.${subSubCounter}`
          rows.push({
            key: `${scopeKey}.${subKey}`,
            title: subValue.title,
            description: subValue.description,
            questionNumber: subSubQuestionNumber,
            isHeader: false,
          })
        })
      } else {
        rows.push({
          key: scopeKey,
          title: scopeValue.title,
          description: scopeValue.description,
          questionNumber: subQuestionNumber,
          isHeader: false,
        })
      }
    }
  })

  const columns: FormField[] = yearFields.map((yearField: string, index: number) => ({
    questionNumber: `${questionNumber}.${index + 1}`,
    field: yearField,
    title: { en: yearColumns[index], et: yearColumns[index] },
    type: "number" as const,
    minimum: 0,
  }))

  return {
    questionNumber,
    field: fullFieldKey,
    type: "table",
    title: schemaField.title,
    description: schemaField.description,
    helpText: schemaField.helpText,
    required: isRequired,
    columnHeaders: yearColumns,
    isYearlyTable: true,
    rows,
    columns,
    readOnly: false,
    ...(schemaField.helpText ? { helpText: schemaField.helpText } : {}),
    ...(schemaField.dependency
      ? { dependency: normalizeDependencyPaths(schemaField.dependency, fieldKey, prefix) }
      : {}),
  }
}

/**
 * Normalizes question_Id in a dependency array.
 * Adds the parent path prefix if not already nested.
 */
export function normalizeDependencyPaths(dependencies: Dependency[], fieldKey?: string, prefix?: string): Dependency[] {
  const parentPath = fieldKey ? fieldKey.split(".").slice(0, -1).join(".") : ""

  return dependencies.map((dep) =>
    dep.questionId.includes(".")
      ? dep
      : {
          ...dep,
          questionId: `${prefix ? prefix + "." : ""}${parentPath ? parentPath + "." : ""}${dep.questionId}`,
        }
  )
}

/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

interface LocalizedText {
  en: string
  et: string
}

export type FieldType =
  | "text"
  | "number"
  | "date"
  | "select"
  | "table"
  | "checkbox"
  | "file"
  | "textarea"
  | "multiselect"
  | "radio"
  | "boolean-radio"
  | "activity-selector"

export type Condition = {
  minValue?: number
  maxValue?: number
  equals?: number | string | boolean
  includes?: string
  startsWith?: string | string[]
  minLength?: number
  maxLength?: number
  notEmpty?: boolean
}

export interface Dependency {
  questionId: string
  condition: Condition
}

// Base interface for all form fields, containing common properties
export interface FormFieldBase {
  questionNumber: string
  field: string
  title?: LocalizedText
  description?: LocalizedText
  placeholder?: LocalizedText
  required?: boolean
  helpText?: LocalizedText
  dependency?: Dependency[]
  maxItems?: number
  isYearField?: boolean
}

// Specific field types extending the base interface
export interface TextField extends FormFieldBase {
  type: "text"
}

export interface NumberField extends FormFieldBase {
  type: "number"
  minimum?: number
  maximum?: number
}

export interface DateField extends FormFieldBase {
  type: "date"
  format?: string
}

export interface SelectField extends FormFieldBase {
  type: "select"
  enum: LocalizedText[]
}

export interface MultiSelectField extends FormFieldBase {
  type: "multiselect"
  enum: LocalizedText[]
}

export interface ActivitySelectField extends FormFieldBase {
  type: "activity-selector"
  enum: LocalizedText[]
}

export interface RadioSelectField extends FormFieldBase {
  type: "radio"
  enum: LocalizedText[]
}

export interface BooleanRadioField extends FormFieldBase {
  type: "boolean-radio"
}

export interface CheckboxField extends FormFieldBase {
  type: "checkbox"
}

export interface FileField extends FormFieldBase {
  type: "file"
}

export interface TextareaField extends FormFieldBase {
  type: "textarea"
}

export interface TableField extends FormFieldBase {
  type: "table"
  columns: FormField[]
  predefinedColumns?: FormField[] // For predefined rows in energy-source-table
  readOnly: boolean
  columnHeaders?: string[]
  isYearlyTable?: boolean
  itemLabel?: LocalizedText
  rows?: {
    key: string
    title?: LocalizedText
    description?: LocalizedText
    questionNumber: string
    isHeader?: boolean
  }[]
  isEnergySourceTable?: boolean
  predefinedRows?: {
    key: string
    title?: LocalizedText
    description?: { en: string; et: string }
    questionNumber: string
  }[]
}

// Union type for all possible form fields
export type FormField =
  | TextField
  | NumberField
  | DateField
  | SelectField
  | TableField
  | CheckboxField
  | FileField
  | TextareaField
  | MultiSelectField
  | RadioSelectField
  | BooleanRadioField
  | ActivitySelectField

// Interface for a form section, containing a title and a list of fields
export interface FormSection {
  sectionNumber: string
  sectionTitle?: LocalizedText
  sectionDescription?: LocalizedText
  helpText?: LocalizedText
  fields: FormField[]
  dependency?: Dependency[]
}

// Type for a field in the JSON schema
export type SchemaField = {
  type: string
  title?: LocalizedText
  description?: LocalizedText
  placeholder?: LocalizedText
  helpText?: LocalizedText
  format?: string
  properties?: Record<string, SchemaField>
  items?: SchemaField
  required?: string[]
  minimum?: number
  maximum?: number
  label?: LocalizedText
  dependency?: Dependency[]
  itemLabel?: LocalizedText
  maxItems?: number
  "x-display"?: string
  "x-year-columns"?: string[]
  isHeader?: boolean
  isYearField?: boolean
  selectOptions?: LocalizedText[]
}

// Interface for the input schema
export interface SchemaInput {
  properties: Record<string, SchemaField>
  required?: string[]
  type?: string
  title?: LocalizedText
  description?: LocalizedText
}

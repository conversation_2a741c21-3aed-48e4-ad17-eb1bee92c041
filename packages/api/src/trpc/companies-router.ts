/* eslint-disable @typescript-eslint/no-unused-vars */
/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import type { CompanyDocument } from "@impactly/domain/companies/elastic"
import type { companyEmissionSchema } from "@impactly/domain/companies/index"
import type { GHGYearData } from "@impactly/integration/backend"
import type { TRPCRouterRecord } from "@trpc/server"
import { companyEventStore } from "@impactly/domain/companies/index"
import { getPortalUserRoles } from "@impactly/domain/portal-users/utils/get-portal-user-roles"
import { service as integrationService } from "@impactly/integration/backend"
import { getTranslations } from "next-intl/server"
import { z } from "zod"

// import { createIndexDataLoaderFactory } from "@kreios/api/utils"

import { createLocalizedError, ErrorTranslationKeys } from "../utils/localized-error"
import { protectedProcedure, publicProcedure } from "./trpc"

const idSchema = z.string().describe("Aggregate ID")

const getCurrentYear = () => new Date().getFullYear()
const getMinAllowedYear = () => getCurrentYear() - 4
const getMaxAllowedYear = () => getCurrentYear()

const yearSchema = z
  .number()
  .int()
  .min(getMinAllowedYear(), `Year must be between ${getMinAllowedYear()} and ${getMaxAllowedYear()}`)
  .max(getMaxAllowedYear(), `Year must be between ${getMinAllowedYear()} and ${getMaxAllowedYear()}`)

// GHG Data validation schemas
// These schemas validate the complete GHG calculator form data structure
// matching the GHGFormData type from @impactly/integration/backend/types
const stationaryRowSchema = z.object({
  fuel: z.string().min(1, "Fuel type is required"),
  consumption: z.string(),
  unit: z.string(),
  area: z.string(),
  areaUnit: z.string(),
})

const mobileRowSchema = z.object({
  category: z.string().min(1, "Category is required"),
  consumption: z.string().optional(),
  unit: z.string().optional(),
  distance: z.string().optional(),
  distanceUnit: z.string().optional(),
  expense: z.string().optional(),
  fuelType: z.string().optional(),
})

const fugitiveRowSchema = z.object({
  name: z.string().min(1, "Gas name is required"),
  amount: z.string().optional(),
  unit: z.string().min(1, "Unit is required"),
})

const animalRowSchema = z.object({
  animal: z.string().min(1, "Animal type is required"),
  count: z.string(),
  fieldPercentage: z.string(),
})

const fertiliserRowSchema = z.object({
  type: z.string().min(1, "Fertiliser type is required"),
  amount: z.string(),
  unit: z.string().min(1, "Unit is required"),
})

const electricityRowSchema = z.object({
  type: z.string().min(1, "Electricity type is required"),
  consumption: z.string(),
  unit: z.string(),
  cost: z.string(),
  hasPPA: z.string(),
})

const districtEnergyRowSchema = z.object({
  category: z.string().min(1, "Category is required"),
  consumption: z.string(),
  unit: z.string(),
  area: z.string(),
  areaUnit: z.string(),
})

const ghgFormDataSchema = z.object({
  stationary: z.object({
    fuelRows: z.array(stationaryRowSchema),
    biomassRows: z.array(stationaryRowSchema),
  }),
  mobile: z.array(mobileRowSchema),
  fugitive: z.array(fugitiveRowSchema),
  agricultural: z.object({
    animalRows: z.array(animalRowSchema),
    fertiliserRows: z.array(fertiliserRowSchema),
  }),
  electricity: z.array(electricityRowSchema),
  district: z.array(districtEnergyRowSchema),
  metadata: z
    .object({
      lastSaved: z.string().optional(),
      version: z.string().optional(),
    })
    .optional(),
  calculatedTotals: z
    .object({
      sectionTotals: z.record(z.string(), z.number()),
      scope1Total: z.number().min(0, "Scope 1 total must be non-negative"),
      scope2Total: z.number().min(0, "Scope 2 total must be non-negative"),
      scope1AndScope2TotalEmissions: z.number().min(0, "Total emissions must be non-negative"),
    })
    .optional(),
})

const gateway = container.resolve("gateway")

// const createIndexDataLoader = createIndexDataLoaderFactory(gateway)

type GhgEmission = z.infer<typeof companyEmissionSchema>

// Utility function to transform GHG emission data
const transformEmissionData = (data?: GhgEmission[] | null) => {
  if (!data || data.length === 0) {
    return { years: [], transformedData: [] }
  }

  const years = Array.from(new Set(data.map((item) => item.year).filter((year) => year !== undefined))).sort(
    (a, b) => b - a
  )
  const OUTPUT_ORDER = ["ghg_scope1", "ghg_scope2_locationbased", "ghg_scope3_total", "ghg_total_emissions"] as const
  const orderedScopes = OUTPUT_ORDER.filter((scope) => data.some((item) => item.scope === scope))

  const getEmissionValue = (year: number, scope: string) => {
    if (scope !== "ghg_scope2_locationbased") {
      const emission = data.find((item) => item.year === year && item.scope === scope)
      return emission?.value ? `${emission.value} ${emission.unit}` : ""
    }

    const locationBased = data.find((item) => item.year === year && item.scope === "ghg_scope2_locationbased")
    if (locationBased?.value) return `${locationBased.value} ${locationBased.unit}`

    const marketBased = data.find((item) => item.year === year && item.scope === "ghg_scope2_marketbased")
    return marketBased?.value ? `${marketBased.value} ${marketBased.unit}` : ""
  }

  const transformedData: Record<string, string>[] = orderedScopes.map((scope) => ({
    scope,
    ...Object.fromEntries(years.map((year) => [year.toString(), getEmissionValue(year, scope)])),
  }))

  return {
    years,
    transformedData,
  }
}

export const companiesRouter = {
  history: protectedProcedure.input(idSchema).query(async ({ input, ctx: { portalUser } }) => {
    const portalUserId = portalUser.aggregateId

    const watchlist = await gateway.findFirst("watchlists", {
      bool: {
        must: [{ term: { portalUserId: portalUserId } }],
      },
    })

    if (!watchlist) throw createLocalizedError("NOT_FOUND", ErrorTranslationKeys.WATCHLIST_NOT_FOUND)

    const companyIds = watchlist.entries.map((entry) => entry.companyId)

    if (!companyIds.includes(input)) throw createLocalizedError("FORBIDDEN", ErrorTranslationKeys.FORBIDDEN)

    const { events } = await companyEventStore.getEvents(input)
    return events
  }),
  get: protectedProcedure.input(idSchema).query(async ({ input: id, ctx: { portalUser } }) => {
    const t = await getTranslations()
    const { isCompanyUser, isObserver } = getPortalUserRoles(portalUser)

    if ((isCompanyUser || isObserver) && portalUser.companyId !== id)
      throw createLocalizedError("FORBIDDEN", t(ErrorTranslationKeys.FORBIDDEN))

    const { aggregate } = await companyEventStore.getAggregate(id)
    if (!aggregate) throw createLocalizedError("NOT_FOUND", t(ErrorTranslationKeys.COMPANY_NOT_FOUND))

    const companyScoring = await integrationService.getCompanyDetailScoring({ companyId: parseInt(id) })

    const hasLowManagementScore =
      companyScoring.source_type === "ai" &&
      Object.values(companyScoring.data).every((category) =>
        ["Low", "Very Low"].includes(category.management_score_status)
      )

    const ghgEmissionData = transformEmissionData(aggregate.latestGhgEmission)

    return {
      ...aggregate,
      companyScoringData: { companyScoring, hasLowManagementScore },
      ghgEmissionData,
    }
  }),
  saveGHGData: protectedProcedure
    .input(
      z.object({
        year: yearSchema,
        data: ghgFormDataSchema,
      })
    )
    .mutation(async ({ input: { year, data }, ctx: { portalUser } }) => {
      const companyId = portalUser.companyId
      const t = await getTranslations()

      if (!companyId) throw createLocalizedError("FORBIDDEN", t(ErrorTranslationKeys.FORBIDDEN))

      try {
        const saveResponse = await integrationService.saveGHGData(year, parseInt(companyId), data)
        return saveResponse
      } catch (_error) {
        throw createLocalizedError("INTERNAL_SERVER_ERROR", t(ErrorTranslationKeys.GHG_SAVE_FAILED))
      }
    }),
  getGHGData: protectedProcedure
    .input(
      z.object({
        year: yearSchema,
      })
    )
    .query(async ({ input: { year }, ctx: { portalUser } }) => {
      const companyId = portalUser.companyId
      const t = await getTranslations()

      if (!companyId) throw createLocalizedError("FORBIDDEN", t(ErrorTranslationKeys.FORBIDDEN))

      try {
        const ghgData = await integrationService.getGHGData(year, parseInt(companyId))
        return ghgData
      } catch (error) {
        if (error instanceof Error && error.message.startsWith("errors.")) {
          throw error
        }
        throw createLocalizedError("INTERNAL_SERVER_ERROR", t(ErrorTranslationKeys.GHG_FETCH_FAILED))
      }
    }),
  getAllGHGYears: protectedProcedure.query(async ({ ctx: { portalUser } }) => {
    const companyId = portalUser.companyId
    const t = await getTranslations()

    if (!companyId) throw createLocalizedError("FORBIDDEN", t(ErrorTranslationKeys.FORBIDDEN))

    try {
      const yearlyData = await integrationService.getAllGHGYears(parseInt(companyId))
      return yearlyData
    } catch (_error) {
      throw createLocalizedError("INTERNAL_SERVER_ERROR", t(ErrorTranslationKeys.GHG_FETCH_FAILED))
    }
  }),

  getGHGDataForYear: protectedProcedure
    .input(
      z.object({
        year: z.number().int().min(2000).max(2100),
      })
    )
    .query(async ({ input: { year }, ctx: { portalUser } }): Promise<GHGYearData> => {
      const companyId = portalUser.companyId
      const t = await getTranslations()

      if (!companyId) throw createLocalizedError("FORBIDDEN", t(ErrorTranslationKeys.FORBIDDEN))

      try {
        const ghgData = await integrationService.getGHGDataForYear(parseInt(companyId), year)
        return ghgData
      } catch (error) {
        if (error instanceof Error && error.message.startsWith("errors.")) {
          throw error
        }
        throw createLocalizedError("INTERNAL_SERVER_ERROR", t(ErrorTranslationKeys.GHG_FETCH_FAILED))
      }
    }),
  search: publicProcedure
    .input(z.object({ limit: z.number().min(1).max(20).optional().default(5), search: z.string().optional() }))
    .query(async ({ input: { limit, search } }) => {
      if (!search || search.length <= 1) {
        return []
      }

      const { documents } = await gateway.search<CompanyDocument, never>({
        query: {
          bool: {
            should: [
              { match_phrase_prefix: { label: search } },
              { fuzzy: { label: { value: search, fuzziness: "AUTO" } } },
            ],
          },
        },
        index: "companies",
        size: limit,
      })
      return documents.map((doc) => ({
        aggregateId: doc.aggregateId,
        name: doc.label,
      }))
    }),
  list: protectedProcedure
    .input(
      z.object({
        page: z.number().min(1).optional().default(1),
        limit: z.number().min(5).optional().default(10),
        search: z.string().optional(),
        sort: z.record(z.enum(["asc", "desc"])).optional(),
      })
    )
    .query(async ({ input: { limit, page, search, sort }, ctx: { portalUser } }) => {
      const { isAdmin, isClientAdmin } = getPortalUserRoles(portalUser)

      if (!isAdmin && !isClientAdmin) {
        throw createLocalizedError("FORBIDDEN", ErrorTranslationKeys.FORBIDDEN)
      }

      const { total, documents } = await gateway.search<CompanyDocument, never>({
        query: {
          bool: {
            should: search
              ? [{ match_phrase_prefix: { label: search } }, { fuzzy: { label: { value: search, fuzziness: "AUTO" } } }]
              : [],
          },
        },
        index: "companies",
        from: (page - 1) * limit,
        size: limit,
        sort: [
          ...(sort
            ? Object.entries(sort)
                .filter(([field]) => field !== "businessActivities")
                .map(([field, order]) => ({ [`${field}.keyword`]: order }))
            : []),
        ],
      })

      return {
        count: total,
        data: await Promise.all(
          documents.map(async (company) => ({
            ...company,
          }))
        ),
        facets: [],
      }
    }),
} satisfies TRPCRouterRecord

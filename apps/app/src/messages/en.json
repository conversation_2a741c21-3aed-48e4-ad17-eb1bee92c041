{"breadcrumbs": {"paths": {"loading": "Loading...", "admin": "Admin", "companies": "Companies", "surveys": "Survey Batches", "survey": "Survey", "portfolios": "Portfolios", "self-assesment": "Self Assessment", "self-assesments": "Self Assessments", "assessment-batches": "Assessment Batches", "company-surveys": "Company Surveys", "self-assessments": "Self Assessments", "ghg-calculator": "GHG Calculator", "report": "Report"}}, "cookieConsent": {"message": "This platform uses strictly necessary cookies that are required for it to function. We also use optional analytics cookies to understand how the platform is used and improve your experience. You can choose to accept or reject these optional cookies below. For more details, see our <Link>Cookie Policy</Link>.", "accept": "Accept", "decline": "Decline"}, "copilot": {"title": "AI Copilot", "currentChat": "Current chat", "newChat": "New Chat", "send": "Send message", "sendMessage": "Send a message.", "emptyScreen": {"title": "Hello! I'm <PERSON><PERSON>pi<PERSON>.", "description": "I'm here to help you navigate the platform and answer your ESG-related questions. How can I assist you today?"}, "history": {"title": "Chat History", "clearHistory": "Clear history", "noHistory": "No chat history", "dialog": {"title": "Are you absolutely sure?", "description": "This will permanently delete your chat history and remove your data from our servers.", "cancel": "Cancel", "confirm": "Delete", "deleteChat": "Delete Chat"}}}, "sidebar": {"title": "Impactly", "subtitle": "Client Portal", "navigation": {"title": "Portal", "Dashboard": "Dashboard", "Search": "Search", "Notifications": "Notifications", "Portfolios": "Portfolios", "Companies": "Companies", "MyCompany": "My Company", "Surveys": "Surveys", "EvaluationRequests": "Evaluation Requests", "SurveyBatches": "Survey Batches", "selfAssessment": "Self Assessment", "AssessmentBatches": "Assessment Batches", "selfAssessments": "Self Assessments", "GHGCalculator": "GHG Calculator"}, "theme": "Toggle Theme", "loggedOut": "Logged out", "loggingOut": "Logging out...", "logoutError": "Failed to log out", "logOut": "Log Out"}, "assessmentBatches": {"title": "Assessment Batches Dashboard", "searchPlaceholder": "Search assessment batches...", "export": "Export", "addNewBatch": "Add New Self Assessment Batch", "batchCard": {"companies": "Companies"}, "addNewBatchDialog": {"title": "Add New Self Assessment Batch", "description": "Please enter the name of the new batch.", "inputLabel": "Name", "inputPlaceholder": "Enter assessment batch name", "save": "Save", "cancel": "Cancel", "successMessage": "<PERSON><PERSON> added successfully.", "errorMessage": "Error adding batch. Please try again."}, "emptyBatch": {"noResults": "No results.", "noBatchCreated": "No batches have been created. Please create a batch to see the results on this dashboard."}}, "surveyBatches": {"title": "Survey Batches Dashboard", "searchPlaceholder": "Search batches...", "export": "Export", "addNewBatch": "Add New Batch", "batchCard": {"companies": "Companies"}, "addNewBatchDialog": {"title": "Add New Batch", "description": "Please enter the name of the new batch.", "inputLabel": "Name", "inputPlaceholder": "Enter batch name", "save": "Save", "cancel": "Cancel", "successMessage": "<PERSON><PERSON> added successfully.", "errorMessage": "Error adding batch. Please try again."}, "emptyBatch": {"noResults": "No results.", "noBatchCreated": "No batches have been created. Please create a batch to see the results on this dashboard."}}, "selfAssessments": {"selfAssessments": "Self Assessments", "searchPlaceholder": "Search self-assessments...", "breadcrumbSearchLabel": "<PERSON><PERSON>", "import": "Import", "export": "Export", "sendReminder": "Send Reminder", "sendSurvey": "Send Self Assessment", "surveySubmittedSuccessfully": "Self assessment submitted successfully.", "overdueSurvey": "This self assessment is overdue.", "cancel": "Cancel", "cancelSurveyDialog": {"title": "Cancel Selected Self Assessments", "description": "Are you sure you want to cancel the selected Self Assessments? This action cannot be undone.", "confirmLabel": "Yes, cancel self assessments", "cancelLabel": "No, keep self assessment", "loadingText": "Cancelling Self Assessments", "successText": "Self Assessments canceled successfully", "errorText": "Failed to cancel self assessments", "cancellationAborted": "Cancellation aborted"}, "sendAssessmentDialog": {"title": "Send Self Assessment", "description": "Enter the survey details and recipients for the companies for which you would like to send the self assessment", "confirmLabel": "Confirm", "cancelLabel": "Cancel", "loadingText": "Sending...", "addMore": "Add More", "successMessage": "<PERSON><PERSON> successfully.", "errorMessage": "Error sending self assessment. Please try again.", "columns": {"company": "Company", "template": "Template", "companyContacts": "Company Contacts", "financialYear": "Fiscal Year", "deadline": "Deadline"}}, "sendSurveyDialog": {"title": "Send Survey", "description": "Enter the survey details and recipients for the companies for which you would like to send survey", "confirmLabel": "Confirm", "cancelLabel": "Cancel", "loadingText": "Sending...", "addMore": "Add More", "successMessage": "<PERSON><PERSON> successfully.", "errorMessage": "Error sending survey. Please try again.", "columns": {"company": "Company", "template": "Template", "companyContacts": "Company Contacts", "financialYear": "Fiscal Year", "deadline": "Deadline"}}, "columns": {"survey": "Self Assessment", "status": "Status", "statusChangeTimestamp": "Status Change Timestamp", "lastReminder": "Last Reminder", "template": "Template", "progress": "Progress", "issuer": "Issuer", "deadline": "Deadline", "actions": "Actions", "submissionDate": "Submission Date", "company": "Company"}}, "surveys": {"surveys": "Surveys", "searchPlaceholder": "Search surveys...", "breadcrumbSearchLabel": "<PERSON><PERSON>", "import": "Import", "export": "Export", "sendReminder": "Send Reminder", "sendSurvey": "Send Survey", "surveySubmittedSuccessfully": "Survey submitted successfully.", "overdueSurvey": "This survey is overdue.", "cancel": "Cancel", "cancelSurveyDialog": {"title": "Cancel Selected Surveys", "description": "Are you sure you want to cancel the selected surveys? This action cannot be undone.", "confirmLabel": "Yes, cancel surveys", "cancelLabel": "No, keep surveys", "loadingText": "Cancelling Surveys", "successText": "Surveys canceled successfully", "errorText": "Failed to cancel surveys", "cancellationAborted": "Cancellation aborted"}, "sendSurveyDialog": {"title": "Send Survey", "description": "Enter the survey details and recipients for the companies for which you would like to send the survey", "confirmLabel": "Confirm", "cancelLabel": "Cancel", "loadingText": "Sending...", "addMore": "Add More", "successMessage": "<PERSON><PERSON> successfully.", "errorMessage": "Error sending surveys. Please try again.", "columns": {"company": "Company", "template": "Template", "companyContacts": "Company Contacts", "financialYear": "Fiscal Year", "deadline": "Deadline"}}, "columns": {"survey": "Survey", "status": "Status", "statusChangeTimestamp": "Status Change Timestamp", "lastReminder": "Last Reminder", "template": "Template", "progress": "Progress", "issuer": "Issuer", "deadline": "Deadline", "actions": "Actions", "submissionDate": "Submission Date", "company": "Company"}}, "survey": {"components": {"activitySelector": {"notFound": "No matching activities found.", "moreActivities": "More activities available. Please refine your search to see more specific results.", "searchPlaceholder": "Please enter at least 3 characters.", "placeholder": "Search for an activity"}, "autoSaveIndicator": {"pending": "Saving changes...", "success": "All changes saved"}, "detailDisplay": {"viewExampleDocument": "View Example Document"}, "genericFieldArray": {"remove": "Remove", "add": "Add", "removeSource": "Remove Source", "no": "No", "category": "Category", "yearNotFound": "Year Field not found", "yearlyPlaceholder": "Enter volume"}, "prefilledField": {"hasPrefilledValue": "This field has a prefilled value.", "accept": "Accept", "reject": "Reject"}, "prefilledHeader": {"acceptAll": "Accept all", "rejectAll": "Reject all", "message": "This survey contains prefilled answers. Please review them and accept/reject the answers in bulk or for each individual question."}, "submitQuestionnaireDialog": {"title": "Submit Survey", "submit": "Submit", "cancel": "Cancel", "submitting": "Submitting your survey...", "summaryMessage": "Here is a summary of the files which have been uploaded as evidence:", "thankyouMessage": "Thank you for filling out the survey. Once you submit it, you will no longer be able to edit the responses.", "selfAssessmentTitle": "Submit Self Assessment", "selfAssessmentSubmitting": "Submitting your Self Assessment...", "selfAssessmentThankyouMessage": "Thank you for filling out the Self Assessment. Once you submit it, you will no longer be able to edit the responses."}}, "survey": "Survey", "helpText": "Help Information: Click on a question or its help icon to see additional information and guidance.", "surveySubmittedSuccessfully": "Survey submitted successfully", "pagination": {"start": "Start", "next": "Next", "previous": "Previous", "submit": "Submit"}, "tabs": {"introduction": {"title": "Introduction", "description": ""}, "general": {"title": "General", "description": "The general section is required to provide basic information on the Company's activity"}, "climate_impact": {"title": "Climate Impact", "description": ""}, "green_transition": {"title": "Green Transition", "description": ""}, "nature": {"title": "Nature", "description": ""}, "social_governance": {"title": "Social & Governance", "description": ""}, "nfrd_reporting": {"title": "NFRD Reporting", "description": ""}}, "clearForm": {"title": "Clear Form", "description": "Are you sure you want to clear the form? This action cannot be undone.", "confirmLabel": "Yes", "cancelLabel": "No"}}, "companyProfile": {"breadcrumbSearchLabel": "Company", "scoringBasis": {"basis": "<PERSON><PERSON>", "publicData": "Public Data", "selfAssessment": "Self Assessment", "basisPublicDescription": "Evaluation is based on information automatically gathered and analyzed by AI from publicly available sources (company website, annual reports, etc.). It reflects an external perspective on your ESG profile.", "basisAssessmentDescription": "Evaluation is based on the information and responses you provided through our ESG self-assessment form. Any publicly collected information about your company's ESG practices and data is hereby excluded."}, "alert": {"title": "Limited Version", "description": "You are currently using a demo version of the application. Some functionalities may be disabled."}, "tabs": {"companyOverview": "Company Overview", "esgAssessment": "ESG Assessment", "assessmentHistory": "Assessment History", "history": "History", "evaluationOverview": "Evaluation Overview"}, "companyOverview": {"summary": {"title": "Summary", "companyEvaluated": "Company Evaluated", "evaluated": "Evaluated", "name": "Name", "companyActivities": "Company Activities", "readMore": "read more", "readLess": "read less", "noData": "No company about available"}, "esgRisks": {"title": "Company ESG Assessment", "noRisks": "At the current moment we do not have any ESG risks for this company. This may require a new assessment.", "maturityLevel": "Maturity Level", "aboutTheCompany": "About the Company", "sources": "Sources", "financial": "Financial", "category": "Category", "assessmentDetails": "Assessment Details", "maturityLevels": {"veryLow": "Very Low", "low": "Low", "medium": "Medium", "high": "High", "veryHigh": "Very High"}, "dialog": {"description": "Description", "summary": "Summary", "noSummary": "No summary found", "recommendations": "Recommendations", "noRecommendations": "No recommendations found", "references": "References", "path": "Path", "referenceType": "Reference Type", "close": "Close"}}, "about": {"title": "About the Company", "country": "Country", "employees": "Employees", "website": "Website", "address": "Address", "registrationNumber": "Registration number", "registrationDate": "Registration date", "lastAssessmentDate": "Last assessment date", "noData": "N/A"}, "sources": {"title": "Public Information", "mediaCoverage": "Media Coverage", "officialSources": "Official Sources", "noData": "No data available"}, "financials": {"title": "Financials", "revenue": "Revenue", "profit": "Profit", "assets": "Assets"}}, "esgAssessment": {"esgCategories": {"managementScore": "Management Score", "transparencyScore": "Transparency Score", "summary": "Summary", "selectSubCategory": "Select a subcategory", "industryBestPractices": "Industry Best Practices", "recommendations": "Recommendations", "industryBestPracticesHelpText": "This section lists the industry best practices for the selected category and the company's adherence to them.", "summaryHelpText": "This section provides a summary of the company's ESG performance on the selected category.", "managementScoreHelpText": "Assesses the effectiveness of your company's sustainability management. Reflects your commitment to achieving environmental goals and implementing good governance practices.", "transparencyScoreHelpText": "This score measures the extent to which your company discloses relevant ESG data points. It rewards the act of sharing information, regardless of whether the specific data reflects positive or negative ESG management.", "noData": "We currently do not have enough information from public sources to provide an adequate ESG assessment for this company. Please visit the Self Assessments section to update your data.", "na": "N/A"}, "maturityCard": {"title": "ESG Maturity", "description": "Current ESG Performance", "score": "Score", "noData": "No data available", "categories": {"environmental": "Environmental", "social": "Social", "governance": "Governance"}}, "euTaxonomy": {"title": "EU Taxonomy Eligibility", "description": "Current Eligibility Status", "eligible": "Eligible", "percentage": "Percentage", "footer": "Based on current EU Taxonomy criteria"}, "ebrdRisk": {"title": "EBRD Risk Categories", "headers": {"overall": "Overall", "environmental": "Environmental", "social": "Social"}, "riskLevels": {"low": "Low", "medium": "Medium", "high": "High"}}, "ghgEmissions": {"title": "Reported GHG Emissions", "noData": "No emissions data available", "scope": "<PERSON><PERSON>"}, "assessment": {"title": "Assessment Summary", "noData": "No assessment available", "readMore": "Read more"}, "recommendations": {"title": "Recommendations", "noData": "No assessment available", "readMore": "Read more"}}}, "pagination": {"next": "Next", "previous": "Previous", "pageOf": "Page {currentPage} of {totalPages}"}, "companySelector": {"label": "Company", "searchPlaceholder": "Search company...", "selectPlaceholder": "Select company...", "searchLengthValidation": "Please enter at least 3 characters.", "noCompaniesMatches": "No companies matching the search", "noCompaniesFound": "not yet in our database", "tooManyCompaniesFound": "More entries are available. Please continue refining your search to narrow down the results."}, "companies": {"pageTitle": "Companies", "searchPlaceholder": "Search...", "columns": {"company": "Company", "country": "Country", "mainActivity": "Main Activity", "environmental": "Environmental", "social": "Social", "governance": "Governance", "registrationNumber": "Registration Number", "lastEvaluation": "Last Evaluation"}, "noData": "N/A", "riskLevel": {"high": "High", "low": "Low", "medium": "Medium", "very_high": "Very High", "very_low": "Very Low"}}, "common": {"export": "Export", "exporting": "Exporting...", "import": "Import", "save": "Save", "cancel": "Cancel", "clear": "Clear", "emailPlaceholder": "<PERSON><PERSON>", "previous": "Previous", "next": "Next", "start": "Start", "submit": "Submit", "changeLanguage": "Change Language", "noCompanyName": "NAME_NOT_AVAILABLE", "clientPortal": "Client Portal", "toDashboard": "To Dashboard", "consultancy": {"requestButton": "Request Consultancy", "sending": "Sending...", "successTitle": "Request Sent!", "successMessage": "Your consultancy request has been sent. We'll get back to you soon.", "errorTitle": "Request Failed", "errorMessage": "Failed to send consultancy request. Please try again later.", "title": "Consultancy Request", "confirmMessage": "As part of using the Impactly platform, you are entitled to a free 1-hour session with a sustainability expert. Confirm your free consultancy request.", "confirmButton": "Send Request", "cancelButton": "Cancel"}, "help": {"title": "Help & Support", "description": "Welcome to the Impactly Client Portal help section. Describe what you need help with and we'll get back to you.", "form": {"label": "How can we help you?", "placeholder": "Please describe your question, issue, or what kind of assistance you need...", "sendButton": "Send", "sending": "Sending...", "emptyDescription": "Please describe what you need help with", "successTitle": "Help Request Sent!", "successMessage": "Your help request has been sent. We'll get back to you soon.", "errorTitle": "Request Failed", "errorMessage": "Failed to send help request. Please try again later."}}, "table": {"viewColumns": "View", "toggleColumns": "Toggle Columns", "showAllColumns": "Show All", "resetToDefault": "Reset to De<PERSON>ult", "ascendingLabel": "Asc", "descendingLabel": "Desc", "visibleLabel": "<PERSON>de", "filter": "Filter", "noResults": "No results."}, "pagination": {"rowsPerPage": "Rows per page", "firstPage": "Go to first page", "previousPage": "Go to previous page", "nextPage": "Go to next page", "lastPage": "Go to last page", "pageInfo": "Page {current} of {total}", "rowsSelected": "{selected} of {total} row(s) selected."}, "scoringStatus": {"Very Low": "Very Low", "Low": "Low", "Medium": "Medium", "Good": "Good", "Very Good": "Very Good", "noData": "N/A"}}, "surveyStatus": {"status": "Status", "title": "Survey", "open": "Open", "not_started": "Not Started", "in_progress": "In Progress", "complete": "Completed", "submitted": "Submitted", "sent": "<PERSON><PERSON>", "pending": "Pending", "cancelled": "Cancelled", "processing": "Processing"}, "auth": {"login": {"title": "<PERSON><PERSON>", "email": "Enter your email below to login to your account", "oauth": "Login into your account with", "oauthMultiple": "Choose provider to login into your account", "emailLabel": "Email", "emailPlaceholder": "<EMAIL>", "signInWithEmail": "Send Invitation Email", "sendingEmail": "Sending...", "toDashboard": "To Dashboard", "clientPortal": "Client Portal", "userNotFound": "Your email address is not registered in our system.", "requestAccess": "Request Access", "error": "Something went wrong, please contact our support <NAME_EMAIL> for assistance."}, "requestAccess": {"title": "Request Access", "description": "Fill out the form below to request access to the Impactly Client Portal.", "emailLabel": "Email Address", "companyName": "Company Name", "cancel": "Cancel", "submit": "Submit Request", "successTitle": "Request Submitted!", "successMessage": "Your access request has been submitted. We'll review it and get back to you soon.", "backToLogin": "Back to Login", "clientPortal": "Client Portal", "errors": {"validationError": "Please fill in all required fields correctly.", "emailRequired": "Email address is required.", "emailInvalid": "Please enter a valid email address.", "companyNameRequired": "Company name is required.", "submitFailed": "Failed to send request access notification.", "networkError": "Network error occurred. Please try again.", "unexpectedError": "An unexpected error occurred. Please try again."}}, "verifyRequest": {"title": "Check your email", "description": "A sign in link has been sent to your email address.", "returnToLogin": "Return to Login"}, "error": {"title": "Authentication Error", "description": "There was an error during the authentication process. Please try again.", "returnToLogin": "Return to Login"}}, "submission": {"description": "Fill out the form below to begin your journey with us", "form": {"companyNamePlaceholder": "Company name", "emailDescription": "For receiving the notification when Evaluation is ready", "emailLabel": "Email Address", "emailPlaceholder": "Enter your work email", "nameLabel": "Your Name", "namePlaceholder": "Enter your full name", "sustainabilityLabel": "Sustainability Data Overlap Inquiry", "sustainabilityDescription": "I would like information on the extent to which the sustainability data published by my company overlaps with the sustainability questionnaire prepared by the Estonian Banking Association.(large companies only)", "supplyChainLabel": "Supply Chain Sustainability Information", "supplyChainDescription": "I want more information on how to gather and analyze my supply chain sustainability.", "aiLabel": "AI for Sustainability Reporting", "aiDescription": "I would like additional information on how artificial intelligence can help simplify processes and data collection related to sustainability reporting.", "termsLabel": "Terms and Privacy Agreement", "termsDescription": "I agree to the <termsLink>Terms of Service</termsLink> and <privacyLink>Privacy Policy</privacyLink>", "submit": "Get Started", "processing": "Processing...", "optionalText": "optional", "companyLabel": "Company", "placeholder": "Select company...", "searchPlaceholder": "Search company...", "emptyStateWithCreate": "Please enter at least 3 characters.", "emptyStateWithoutCreate": "No companies matching the search", "notInDatabase": "not yet in our database", "refineSearch": "More entries are available. Please continue refining your search to narrow down the results."}, "processing": {"message": "Processing your sustainability profile..."}, "dialog": {"title": "Thank you for your submission", "close": "Close", "description": "You will receive the report on your e-mail address once available. You can close this page now."}}, "terms": {"title": "Terms of Service", "welcome": "These Terms of Service (\"Terms\") govern your use of the services provided by Impactly (\"Impactly\", \"we\", \"us\", or \"our\"), including access to our ESG and business analytics platform (\"Service\"). By accessing or using the Service, you agree to these Terms.", "sections": {"acceptance": {"title": "Platform Access and Purpose", "content": "Impactly operates an ESG data platform designed to provide businesses with access to tailored analytics, benchmarking comparisons, summaries, and actionable insights."}, "platform": {"title": "Platform Access and Purpose", "items": ["Impactly operates an ESG (environmental, social, and governance) data platform designed to provide businesses with access to tailored analytics, benchmarking comparisons, summaries, and actionable insights.", "The platform uses a combination of publicly available data sourced from official registries and third-party providers, as well as data voluntarily contributed by users. Businesses may choose to submit their own ESG and operational information to enrich their profiles and receive more customized results.", "By using the platform, users acknowledge and agree that <PERSON>ly will process and display both public and user-submitted data to generate ESG summaries, company profiles, and comparative analytics that may be visible to other platform users.", "Users may use Impactly platform data for their own internal purposes only. Any redistribution, resale, sublicensing, or commercial reuse of Impactly-generated content or reports is strictly prohibited.", "Users may withdraw their data and request deletion at any time <NAME_EMAIL>. Requests will be honored within a reasonable timeframe unless data retention is required by law or regulation."]}, "access": {"title": "Free Access and Premium Offerings", "items": ["Access to the platform is granted free of charge to companies invited to participate. This license includes the ability to upload data, receive basic ESG and business analytics, and view benchmarking tools.", "Premium features—such as in-depth reports, ESG recommendations, regulatory alignment outputs, and sector-specific insights—are offered under separate pricing and agreements."]}, "rights": {"title": "Data Usage Rights", "items": ["By submitting data to the platform, users grant Impactly a non-exclusive, worldwide, royalty-free license to use, display, analyze, and distribute submitted data on the platform. This includes the generation of ESG summaries, organization-specific profiles, and comparative insights shared within the Impactly user environment.", "Users are responsible for ensuring the legitimacy and authorization of the data they provide.", "Some of the data used in the Service is sourced from public corporate websites, official public registries and third-party data providers. Impactly follows best practices in the collection, processing, and storage of data and makes every effort to ensure that the information reflected in its services is accurate and up to date. However, Impactly does not guarantee the accuracy, completeness, or reliability of data provided to it by third parties or obtained from public sources, and shall not be liable for any loss or damage arising from the use of such data."]}, "ai": {"title": "Artificial Intelligence and Disclaimers", "items": ["Impactly leverages artificial intelligence (AI) to process submitted and publicly available data to generate insights. Due to the nature of AI, the platform may experience occasional delays or variability in output quality. Impactly makes no guarantees as to the accuracy, completeness, or fitness for a specific purpose of AI-generated reports, recommendations, or insights.", "Users must independently verify any critical data and accept that Impactly is not responsible for any damages, direct or indirect, arising from the use of its platform, reports, or outputs."]}, "responsibility": {"title": "Client Responsibility and Data Awareness", "items": ["By using the Service, users confirm that they understand and accept that business-related information—whether presented individually or as part of a dataset—may change significantly at any time without notice, regardless of whether those changes originate from <PERSON><PERSON>, the data subject, or a third-party source.", "Access credentials are personal and must not be shared with third parties. Users are responsible for all actions taken under their account.", "Users must not use the platform’s AI capabilities for purposes outside the scope of business and ESG data analysis, including but not limited to data scraping, reverse engineering, or generating outputs unrelated to the Service’s purpose."]}, "conduct": {"title": "Lawful Use", "items": ["Users must use the platform only for lawful and authorized business purposes. Misuse, unauthorized access, and data tampering are strictly prohibited.", "Users must not attempt to overload, disrupt, or interfere with the technical operation of the platform."]}, "disclaimer": {"title": "Limitation of Liability", "items": ["All data, insights, and reports provided by Impactly are delivered \"as is\". Impactly does not guarantee the accuracy, reliability, or completeness of any content generated on the platform. Impactly is not liable for any direct, indirect, incidental, or consequential damages resulting from the use of the Service or reliance on the information provided therein."]}, "law": {"title": "Governing Law and Dispute Resolution", "items": ["These Terms are governed by the laws of the Republic of Estonia. Any disputes arising out of or in connection with these Terms shall be resolved in accordance with Estonian law, with jurisdiction vested in Estonian courts."]}, "changes": {"title": "Updates to Terms", "items": ["We reserve the right to update or modify these Terms at any time. Material changes will be communicated, and continued use of the Service following updates constitutes acceptance of the revised Terms."]}, "contact": {"title": "Contact", "items": ["For questions, support, or data-related requests, please contact <NAME_EMAIL>. Impctly OÜ, Looduse 1, Tabasalu, Estonia"]}}, "footerText1": "Impactly Terms of Service", "footerText2": "Version 1.1 (01.Jan 2025)"}, "privacyPolicy": {"title": "Privacy Policy", "effective": "<b>Effective Date:</b> 01 June 2025", "welcome": "At Impactly (\"Impactly\", \"we\", \"us\", or \"our\"), we are committed to protecting and respecting your privacy. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our website www.impactly.eu, our ESG data platform, and related services (collectively, the \"Service\").\nAll personal data is processed in accordance with Regulation (EU) 2016/679 of the European Parliament and of the Council of 27 April 2016 on the protection of natural persons with regard to the processing of personal data and on the free movement of such data, and repealing Directive 95/46/EC (General Data Protection Regulation, \"GDPR\").", "sections": {"controller": {"title": "Data Controller", "content": "<PERSON>ly OÜ is the Data Controller of your information."}, "information": {"title": "Information We Collect and Process", "content": "We collect and process information in different ways, focusing on the minimum necessary to provide and improve our Service.", "items": [{"subtitle": "Information You Directly Provide", "subitems": ["When you register for our Service or contact us, you may provide personal information such as your <b>name and email address</b>. This information is necessary to create and manage your user account and to communicate with you.", "Registered business users (Companies or Financial Institutions) may submit business-related data to the Service. This includes data for ESG profiles, self-assessments, surveys, or documents uploaded for analysis. While this information is primarily business-related, it may incidentally contain personal data (e.g., names of company representatives or contact details within documents). We process such incidental personal data only as part of the business data, in accordance with our agreements with the business users."]}, {"subtitle": "Publicly Available and Third-Party Data", "subitems": ["We collect information from publicly available sources (e.g., official business registries, public corporate websites) and from third-party data providers as part of our Service. This is generally business information but may include publicly listed names of company officials."]}, {"subtitle": "Automatically Collected Information", "subitems": ["<b>Log Files:</b> Like many websites, we collect information that your browser sends whenever you visit our Service (\"Log Data\"). This Log Data may include your computer's Internet Protocol (\"IP\") address, browser type, browser version, the pages of our Service that you visit, the time and date of your visit, and time spent on those pages. This information is used for service operation, security, and understanding user interaction with our platform.", "<b>Cookies:</b> We use cookies and similar tracking technologies as described in our Cookie Policy. Strictly necessary cookies are used for core functionalities like session management and language preferences. Analytics cookies are used with your consent to help us understand user interaction and improve our Service."]}]}, "usage": {"title": "How We Use Your Information", "content": "We use the information we collect, including personal data, for the following purposes:", "items": ["To provide, operate, maintain, and improve our Service.", "To create and manage user accounts and authenticate users.", "To process business data using our platform, including analysis with AI, to generate ESG profiles, reports, and insights for our users.", "To enable Companies to review and manage their ESG data profiles.", "To enable Financial Institutions to browse company profiles and receive data based on appropriate agreements.", "For platform analytics, error tracking, and to understand user interaction to improve our Service (primarily using aggregated or anonymized data where possible).", "To communicate with you, including responding to your inquiries and sending service-related information and updates.", "To comply with legal obligations and enforce our policies, including our Terms of Service."]}, "legal": {"title": "Legal Basis for Processing Your Personal Data", "content": "Our legal basis for collecting and using the personal information described above depends on the personal data and the specific context:", "items": [{"subtitle": "Performance of a Contract", "subitems": ["Processing your name, email, and IP address is necessary to provide the Service to you according to our Terms of Service, including account creation and enabling platform functionalities."]}, {"subtitle": "Consent", "subitems": ["The use of non-essential analytics cookies, as detailed in our Cookie Policy.", "Sending marketing communications, if you opt-in."]}, {"subtitle": "Legitimate Interests", "subitems": ["Processing publicly available business information (which may incidentally include personal data of public figures associated with those businesses) to populate our ESG database and provide our Service.", "Analyzing usage patterns (primarily through aggregated or anonymized data) to improve our Service, ensure security, and prevent misuse.", "Using error tracking to maintain platform stability."]}, {"subtitle": "Legal Obligation", "subitems": ["Processing may be necessary for compliance with a legal obligation to which we are subject."]}]}, "retention": {"title": "Data Retention", "content": "We will retain your personal information only for as long as is necessary for the purposes set out in this Privacy Policy. This means we will retain your account information (name, email) as long as your account is active or as needed to provide you services. We will retain and use your information to the extent necessary to comply with our legal obligations, resolve disputes, and enforce our agreements. Users may request data deletion <NAME_EMAIL>, which we will honor within a reasonable timeframe, subject to legal or regulatory retention requirements. Business data uploaded by users will be retained according to our agreements with those users and our data retention policies for such business data."}, "cookies": {"title": "Cookies", "content": "We use cookies and similar tracking technologies. Some are strictly necessary for our platform's functioning (e.g., session management, language preferences). Others, for analytics and service improvement, are used only with your explicit consent. For detailed information, please see our <Link>Cookie Policy</Link>."}, "processors": {"title": "Data Processors and International Transfers", "content": "To provide our Service, we engage third-party service providers (data processors) who may process personal data on our behalf for purposes such as hosting, data storage, data processing pipelines (including AI model analysis), and user account management. We have data processing agreements in place with these providers where required by GDPR.\nYour personal data is primarily processed within the European Union (EU) or European Economic Area (EEA). Some of our data processors, particularly those providing AI model functionalities, may be located outside the EU/EEA (e.g., in the United States). Where personal data is transferred outside the EU/EEA to a country not deemed to provide an adequate level of data protection by the European Commission, we will implement appropriate safeguards (such as Standard Contractual Clauses) to ensure your personal data remains protected in accordance with GDPR."}, "security": {"title": "Data Security", "content": "We implement appropriate technical and organizational measures to protect the personal data we process against accidental or unlawful destruction, loss, alteration, unauthorized disclosure, or access. This includes measures such as encryption and access controls. However, no method of transmission over the Internet or electronic storage is 100% secure, and we cannot guarantee its absolute security."}, "ai": {"title": "Use of Artificial Intelligence (AI)", "content": "We utilize AI and Large Language Models (LLMs) to process data (publicly available data and data submitted by users) and deliver our Services. The primary purpose is the analysis of business and ESG-related information. While we strive for accuracy, AI-generated content may have limitations."}, "rights": {"title": "Your Data Protection Rights (Under GDPR)", "items": ["<b>The right to access, update or to delete</b> the personal information we have on you.", "<b>The right of rectification:</b> You have the right to have your information rectified if it is inaccurate or incomplete.", "<b>The right to object:</b> You have the right to object to our processing of your Personal Information.", "<b>The right of restriction:</b> You have the right to request that we restrict the processing of your personal information.", "<b>The right to data portability:</b> You have the right to be provided with a copy of the information we have on you in a structured, machine-readable and commonly used format.", "<b>The right to withdraw consent:</b> You have the right to withdraw your consent at any time where <PERSON><PERSON> relied on your consent to process your personal information."], "content": "If you are a resident of the European Economic Area (EEA), you have certain data protection rights:", "footerContent": "If you wish to exercise any of these rights, please contact <NAME_EMAIL>. We may ask you to verify your identity before responding to such requests. You also have the right to complain to a Data Protection Authority about our collection and use of your Personal Information. For more information, please contact your local data protection authority in the EEA."}, "children": {"title": "Children's Information", "content": "Our Service is not intended for use by children under the age of 18. We do not knowingly collect personally identifiable information from children under 18 (or the relevant age of majority). If you believe that your child has provided us with personal information, please contact us immediately, and we will take steps to remove such information from our records."}, "changes": {"title": "Changes to Our Privacy Policy", "content": "We may update this Privacy Policy from time to time. If we make material changes, we will notify you by posting a notice on our website and updating the \"Effective Date\" at the top of this policy. Your continued use of the Service after such updates constitutes your acceptance of the revised policy."}, "acknowledgement": {"title": "Acknowledgement", "content": "By using our Service, you acknowledge that you have read and understood this Privacy Policy. Where consent is the legal basis for processing (e.g., non-essential cookies), this will be obtained separately."}, "contact": {"title": "Contact Us", "content": "If you have any questions about this Privacy Policy, your data protection rights, or our data practices, please contact us: <b>Impactly OÜ</b>, Looduse 1, Tabasalu, Estonia, Email: <b><EMAIL></b>"}}, "footerText1": "Impactly Privacy Policy", "footerText2": "Version 1.0 (01 June 2025)"}, "cookiePolicy": {"title": "<PERSON><PERSON>", "effective": "<b>Effective Date:</b> 01 June 2025", "welcome": "This Cookie Policy explains how Impactly (\"we\", \"us\", or \"our\") uses cookies on our website and data platform (the \"Platform\"). We are committed to protecting your privacy and ensuring transparency in how we use cookies.\n All our services and data, including data processed via cookies, are hosted within the European Union (EU).", "sections": {"what": {"title": "What are Cookies?", "content": "Cookies are small text files that are stored on your device (computer, tablet, smartphone) when you visit a website. They are widely used to make websites work, or work more efficiently, as well as to provide information to the owners of the site."}, "cookies": {"title": "Cookies We Use and Why", "content": "We use a limited number of cookies on our Platform. These are categorized below based on their purpose and legal basis for use.", "items": [{"subtitle": "Strictly Necessary Cookies", "content": "These cookies are essential for the operation of our Platform and to provide you with services you have explicitly requested. They do not require your prior consent and cannot be disabled in our systems.", "subitems": [{"subtitle": "Authentication & Session Management", "subitems": ["<b>Purpose:</b> To manage your user session securely. These cookies allow you to log in and remain authenticated as you navigate the Platform. They are also used to enable core features, such as our copilot chat, by maintaining a stable connection to the service.", "<b>Duration:</b> These cookies last for the duration of your session or for up to 30 days to keep you logged in if you choose."]}, {"subtitle": "User Interface Preferences", "subitems": ["<b>Purpose:</b> To store and remember choices you have made that change the way the Platform looks or behaves. This is necessary to provide a consistent and personalized experience (e.g., remembering your selected language, the state of the sidebar, etc.).", "<b>Duration:</b> Up to 1 year."]}]}, {"subtitle": "Analytics Cookies", "content": "Subject to your explicit consent, we use the following cookies to understand how users interact with our Platform, identify technical issues, and gather insights to improve our services.", "subitems": [{"subtitle": "Platform Analytics and Improvement (via PostHog Cloud EU)", "subitems": ["<b>Description and Purpose:</b> PostHog uses cookies and other technologies, such as JavaScript and browser local storage, to collect and analyze analytical information. These technologies help us understand user interaction with the Platform, identify errors, and analyze user journeys (including through session recording/replay features) to improve usability and service functionality. All data is processed on PostHog's EU cloud servers. Use: These cookies will only be placed and used if you provide your consent.", "<b>Use:</b> These cookies will only be placed and used if you provide your consent.", "<b>Duration:</b> 1 year."]}]}]}, "managing": {"title": "Managing Cookies", "items": ["<b>Strictly Necessary Cookies:</b> As these cookies are essential for the operation of our Platform, they do not require prior consent. However, you can usually control and/or delete cookies as you wish through your browser settings. Please note that disabling these cookies may affect the functionality of the Platform and your ability to use our services.", "<b>Analytics Cookies:</b> Where consent is required for analytics cookies, you will be provided with an option to accept or decline their use. You can withdraw your consent at any time through a cookie settings panel."]}, "marketing": {"title": "No Marketing Tracking", "content": "Currently, we do not implement any marketing tracking cookies on our Platform."}, "changes": {"title": "Changes to This Cookie Policy", "content": "We may update this Cookie Policy from time to time to reflect any changes in our practices or for other operational, legal, or regulatory reasons. We encourage you to review this policy periodically to stay informed about our use of cookies."}, "contact": {"title": "Contact Us", "content": "If you have any questions about this Cookie Policy or our use of cookies, please contact us at: <b>Impactly OÜ</b>, 76901 Tabasalu, Estonia, Email: <b><EMAIL></b>. For more information about our data practices, see our <Link>Privacy Policy</Link>."}}, "footerText1": "Impactly <PERSON>", "footerText2": "Version 1.0 (01 June 2025)"}, "unauthorized": {"clientPortal": "Client Portal", "title": "Unauthorized Access", "description": "It seems you don't have permission to access this page. If you believe this is a mistake, please contact Impactly support team at <Link><EMAIL></Link> for assistance.", "logout": "Logout"}, "consent": {"description": "Thank you for signing in to the Impactly Platform for the first time. Please register in order to fill in your survey.", "usersDetailLabel": "User Details", "companyLabel": "Company Name", "emailLabel": "Email Address", "firstNameLabel": "First Name", "lastNameLabel": "Last Name", "termsAndPrivacyAgreementLabel": "Terms and Privacy Agreement", "termsAndPrivacyAgreementDescription": "I have read and agree to the <termsLink>Terms of Service</termsLink> and <privacyLink>Privacy Policy</privacyLink>.", "dataProcessingLabel": "Data Processing", "dataProcessingDescription": "I agree that any attached files will be retained until no longer required.", "disclaimerLabel": "Disclaimer", "disclaimerDescription": "The auto-filling and evaluation processes are generated with AI based on information provided by the user and publicly available information and has not been verified by a human. Impactly accepts no liability for the accuracy, completeness, or reliability of the content.", "error": "An error occurred. If the problem persists, please contact support.", "register": "Register", "processing": "Registering...", "processingMessage": "Registration in progress......"}, "comingSoon": {"clientPortal": "Client Portal", "title": "Coming Soon", "description": "This feature is not yet available. Please check back later for updates and new features.", "logout": "Logout"}, "emailContent": {"authenticationEmail": {"name": "Impactly Client Portal", "subject": "Log in to Impactly Client Portal", "preview": "Welcome to Impactly Client Portal", "greeting": "Hello", "welcome": "Welcome to Impactly Client Portal. Use the magic link to login", "disclaimer": "If you didn't try to login, you can safely ignore this email.", "button": "Login to Impactly Client Portal", "footer": "This is an automated message. Please do not reply to this email. For assistance please contact our support team at <Link><EMAIL></Link>"}, "sendSurveyEmail": {"selfAssessment": "Self Assessment", "survey": "Survey", "subject": "{type} Invitation: {templateName} for {companyName}", "greeting": "Hello {name},", "invitation": "You have been invited to complete {type} for ", "details": {"label": "{type} Details:", "type": "Type: {templateName}"}, "action": "Please click the button below to access and complete the {type}:", "button": "Access {type}", "footer": "This is an automated message from <PERSON><PERSON>. If you received this email in error, please disregard it. For any questions or assistance, please contact our support team at <Link><EMAIL></Link>"}}, "errors": {"common": {"default": "An error occurred. Please try again.", "unauthorized": "You are not authorized to perform this action.", "forbidden": "You do not have permission to access this resource.", "notFound": "The requested resource was not found.", "badRequest": "Invalid request. Please check your input.", "serverError": "A server error occurred. Please try again later.", "timeout": "The request timed out. Please try again.", "networkError": "Network error. Please check your connection."}, "auth": {"invalidCredentials": "Invalid credentials. Please check your email and password.", "sessionExpired": "Your session has expired. Please log in again.", "emailNotVerified": "Your email is not verified. Please check your inbox.", "userNotFound": "User not found. Please register first."}, "surveys": {"createFailed": "Failed to create survey. Please try again.", "updateFailed": "Failed to update survey. Please try again.", "deleteFailed": "Failed to delete survey. Please try again.", "sendFailed": "Failed to send survey. Please try again.", "notComplete": "Please fill out the full survey before submitting.", "alreadySubmitted": "This survey has already been submitted.", "pastDeadline": "The deadline for this survey has passed.", "notFound": "Survey not found. Please check the survey ID.", "batchNotFound": "Survey batch not found. Please check the batch ID."}, "companies": {"createFailed": "Failed to create company. Please try again.", "updateFailed": "Failed to update company. Please try again.", "deleteFailed": "Failed to delete company. Please try again.", "notFound": "Company not found. Please check the company ID."}, "orders": {"submitFailed": "Error submitting order. Please try again.", "invalidData": "Invalid order data. Please check your input."}, "portalUsers": {"updateFailed": "Failed to update user profile. Please try again.", "notFound": "User profile not found. Please contact support."}, "files": {"uploadFailed": "Failed to upload file. Please try again.", "downloadFailed": "Failed to download file. Please try again.", "invalidType": "Invalid file type. Please upload a supported file type.", "tooLarge": "File is too large. Maximum size is {size}MB."}, "watchlists": {"notFound": "Watchlist not found. Please check the watchlist ID.", "createFailed": "Failed to create watchlist. Please try again.", "updateFailed": "Failed to update watchlist. Please try again.", "deleteFailed": "Failed to delete watchlist. Please try again."}, "evaluationRequests": {"notFound": "Evaluation request not found. Please check the request ID.", "createFailed": "Failed to create evaluation request. Please try again.", "updateFailed": "Failed to update evaluation request. Please try again.", "deleteFailed": "Failed to delete evaluation request. Please try again."}, "ghg": {"saveFailed": "Failed to save GHG emissions data. Please try again.", "fetchFailed": "Failed to fetch GHG emissions data. Please try again.", "dataNotFound": "GHG emissions data not found for the specified year.", "invalidYear": "Invalid year specified. Please select a valid year.", "invalidData": "Invalid GHG emissions data. Please check your input."}}, "toast": {"info": {"notImplemented": "Currently not implemented", "deletionCanceled": "Deletion canceled", "uploadFiles": "Upload files"}, "error": {"submitOrderError": "Error submitting order", "surveySubmitError": "Please fill out full form before submitting", "fileRejected": "{fileName} was rejected due to {errors}", "fileError": "Error: {message}", "comparisonLimitExceeded": "Only {limit} items allowed to compare"}, "success": {"surveySubmittedSuccessfully": "Survey submitted successfully", "selfAssessmentSubmitted": "Self Assessment submitted successfully", "chatDeleted": "<PERSON><PERSON> deleted", "filesUploaded": "Successfully uploaded {count} file{plural}"}, "loading": {"uploadingFiles": "Uploading {count} file{plural}..."}}, "surveyEnums": {"primary": "Primary", "secondary": "Secondary", "tertiary": "Tertiary", "quaternary": "Quaternary", "agriculture": "Agriculture", "manufacturing": "Manufacturing", "services": "Services", "technology": "Technology", "yes": "Yes", "no": "No", "ghg_protocol": "GHG Protocol", "iso_14064": "ISO 14064", "other": "Other", "bureau_veritas": "Bureau Veritas", "bm_certification": "BM Certification", "scope_1": "Scope 1", "scope_1_and_2": "Scope 1 and 2", "scope_1_2_and_3": "Scope 1,2 and 3", "tco2eq_absolute_measurement": "tCO2eq absolute measurement", "tco2eq_as_relation_to_produced_volume_type_in_exact_ratio_eg_02_tco2eq_per_1_t_of_produce": "tCO2eq as relation to produced volume (type in exact ratio, e.g '0.2 tCO2eq/1 t of produce')", "type_in_other": "Type in other", "location_of_it_infrastructure": "Location of IT infrastructure", "production": "Production", "retail": "Retail", "warehouse": "Warehouse", "agricultural_land": "Agricultural land", "forest": "Forest", "other_land_utilized_for_business_purposes": "Other land utilized for business purposes", "office": "Office", "kwh": "kWh", "mwh": "MWh", "gwh": "GWh", "m³": "m³", "tons": "tons", "yes_critically_eg_the_ploughing_of_natural_meadows_construction_works_in_nature_reserves_forest_transformation_deforestation_and_clear-cutting": "Yes, critically (e.g. the ploughing of natural meadows, construction works in nature reserves, forest transformation, deforestation and clear-cutting)", "yes_other_impact": "Yes, other impact", "no_impact": "No impact", "i_dont_know": "I don't know", "personnel_management": "Personnel Management", "remuneration": "Remuneration", "safety_and_health": "Safety & Health", "equality": "Equality", "diversity": "Diversity", "harassment": "Harassment", "child_labour": "Child Labour", "corporate_governance": "Corporate governance", "conflict_of_interest_management": "Conflict of interest management", "respect_of_human_rights_including_labour_right": "Respect of human rights (including labour right)", "prevention_of_money_laundering": "Prevention of money laundering", "whistleblowing": "Whistleblowing", "fraud_and_corruption_prevention": "Fraud and corruption prevention", "tax_transparency": "Tax transparency", "risk_management": "Risk management", "data_security_and_cyber_security": "Data security and cyber security", "code_of_conduct_and_ethics": "Code of conduct and ethics", "high_level_of_monitoring": "High level of monitoring: constantly monitors and controls the activities of suppliers, and checks the information provided by suppliers at maximum feasible capacity.", "medium_level_of_monitoring": "Medium level of monitoring: periodical assessment of sustainability performance of suppliers, often relies on publicly available information.", "low_level_of_monitoring": "Low level of monitoring: mostly relies on publicly available information on suppliers", "no_monitoring": "No monitoring", "a_agriculture_forestry_and_fishing": "A Agriculture, forestry and fishing", "growing_of_cereals_except_rice_leguminous_crops_and_oil_seeds": "Growing of cereals (except rice), leguminous crops and oil seeds", "growing_of_rice": "Growing of rice", "growing_of_vegetables_and_melons_roots_and_tubers": "Growing of vegetables and melons, roots and tubers", "growing_of_sugar_cane": "Growing of sugar cane", "growing_of_tobacco": "Growing of tobacco", "growing_of_fibre_crops": "Growing of fibre crops (e.g. jute, flax, hemp, cotton)", "growing_of_other_non_perennial_crops": "Growing of other non-perennial crops (e.g.flowers and flower seeds etc)", "growing_of_perennial_crops": "Growing of perennial crops (plants that lasts for more than two growing seasons. Grapes and tropical and subtropical fruits and citrus fruits etc.)", "plant_propagation": "Plant propagation (growing of plants for planting and ornamental purposes)", "raising_of_dairy_cattle": "Raising of dairy cattle", "raising_of_other_cattle_and_buffaloes": "Raising of other cattle and buffaloes", "raising_of_horses_and_other_equines": "Raising of horses and other equines", "raising_of_camels_and_camelids": "Raising of camels and camelids", "raising_of_sheep_and_goats": "Raising of sheep and goats", "raising_of_swine_pigs": "Raising of swine/pigs", "raising_of_poultry": "Raising of poultry", "raising_of_other_animals": "Raising of other animals (example dogs & cats)", "mixed_farming": "Mixed farming", "support_activities_for_crop_production": "Support activities for crop production", "support_activities_for_animal_production": "Support activities for animal production", "post_harvest_crop_activities": "Post-harvest crop activities", "seed_process_and_propagation": "Seed process and propagation", "hunting_trapping_and_related_service_activities": "Hunting, trapping and related service activities", "silviculture_and_other_forestry_activities": "Silviculture and other forestry activities", "logging": "Logging", "gathering_of_wild_growing_non_wood_products": "Gathering of wild growing non-wood products", "support_services_to_forestry": "Support services to forestry", "marine_fishing": "Marine Fishing", "freshwater_fishing": "Freshwater Fishing", "marine_aquaculture": "Marine Aquaculture", "freshwater_aquaculture": "Freshwater aquaculture", "b_mining_and_quarrying": "B Mining and quarrying", "mining_of_hard_coal": "Mining of hard coal", "mining_of_lignite": "Mining of lignite", "extraction_of_crude_petroleum": "Extraction of crude petroleum", "extraction_of_natural_gas": "Extraction of natural gas", "mining_of_iron_ores": "Mining of iron ores", "mining_of_uranium_and_thorium_ores": "Mining of uranium and thorium ores", "mining_of_other_non_ferrous_metal_ores": "Mining of other non-ferrous metal ores", "quarrying_of_stone_and_sand_and_clay": "Quarrying of stone and sand and clay", "quarrying_of_ornamental_and_building_stone_limestone_gypsum_chalk_and_slate": "Quarrying of ornamental and building stone, limestone, gypsum, chalk and slate", "operation_of_gravel_and_sand_pits_mining_of_clays_and_kaolin": "Operation of gravel and sand pits mining of clays and kaolin", "mining_of_chemical_and_fertiliser_minerals": "Mining of chemical and fertiliser minerals", "extraction_of_peat": "Extraction of peat", "salt_extraction": "Salt Extraction", "other_mining_and_quarrying_nec": "Other mining and quarrying n.e.c. (abrasive materials and asbestos etc)", "support_activities_for_petroleum_and_natural_gas_extraction": "Support activities for petroleum and natural gas extraction", "support_for_other_mining_and_quarrying": "Support for other mining and quarrying (e.g. exploration service, geological observations, draining and pumping services)", "c_manufacturing": "C Manufacturing", "processing_and_preserving_of_meat": "Processing and preserving of meat", "processing_and_preserving_of_poultry_meat": "Processing and preserving of poultry meat", "production_of_meat_and_poultry_meat_products": "Production of meat and poultry meat products", "processing_and_preserving_of_fish_crustaceans_and_molluscs": "Processing and preserving of fish, crustaceans and molluscs", "processing_and_preserving_of_potatoes": "Processing and preserving of potatoes", "manufacture_of_fruit_and_vegetable_juice": "Manufacture of fruit and vegetable juice", "other_processing_and_preserving_of_fruit_and_vegetables": "Other processing and preserving of fruit and vegetables", "manufacture_of_oils_and_fats": "Manufacture of oils and fats", "manufacture_of_margarine_and_similar_edible_fats": "Manufacture of margarine and similar edible fats", "operation_of_dairies_and_cheese_making": "Operation of dairies and cheese making", "manufacture_of_ice_cream": "Manufacture of ice cream", "manufacture_of_grain_mill_products_starches_and_starch_products": "Manufacture of grain mill products, starches and starch products", "manufacture_of_bread_and_fresh_pastry_goods_and_cakes": "Manufacture of bread and manufacture of fresh pastry goods and cakes", "manufacture_of_rusks_and_biscuits_preserved_pastry_goods_and_cakes": "Manufacture of rusks and biscuits manufacture of preserved pastry goods and cakes", "manufacture_of_macaroni_noodles_couscous_and_similar_farinaceous_products": "Manufacture of macaroni and noodles and couscous and similar farinaceous products", "manufacture_of_other_food_products": "Manufacture of other food products (sugar and cocoa, and etc.)", "manufacture_of_prepared_feeds_for_farm_animals": "Manufacture of prepared feeds for farm animals", "manufacture_of_prepared_pet_foods": "Manufacture of prepared pet foods", "distilling_rectifying_and_blending_of_spirits": "Distilling and rectifying and blending of spirits", "manufacture_of_wine_from_grape": "Manufacture of wine from grape", "manufacture_of_cider_and_other_fruit_wines": "Manufacture of cider and other fruit wines", "manufacture_of_tobacco_products": "Manufacture of tobacco products", "preparation_of_spinning_of_textile_fibres": "Preparation of spinning of textile fibres", "weaving_of_textiles": "Weaving of textiles", "finishing_of_textiles": "Finishing of textiles", "manufacture_of_other_textiles": "Manufacture of other textiles (e.g. knitted and crocheted fabrics, etc)", "manufacture_of_wearing_apparel_except_fur_apparel": "Manufacture of wearing apparel, except fur apparel (e.g. leather clothes, workwear, outerwear, underwear, other wearing apparel and accessories)", "manufacture_of_articles_of_fur": "Manufacture of articles of fur", "manufacture_of_knitted_and_crocheted_apparel": "Manufacture of knitted and crocheted apparel (knitted and crocheted hosiery, other knitted and crocheted apparel)", "tanning_and_dressing_of_leather": "Tanning and dressing of leather (e.g. manufacture of luggage, handbags, saddlery and harness and dressing and dyeing of fur)", "manufacture_of_luggage_handbags_saddlery_and_harness": "Manufacture of luggage, handbags and the like, saddlery and harness", "manufacture_of_footwear": "Manufacture of footwear", "sawmilling_and_planing_of_wood": "Sawmilling and planing of wood", "manufacture_of_products_of_wood_cork_straw_and_plaiting_materials": "Manufacture of products of wood, cork, straw and plaiting materials", "manufacture_of_pulp": "Manufacture of pulp", "manufacture_of_paper_and_paperboard": "Manufacture of paper and paperboard", "manufacture_of_articles_of_paperboard": "Manufacture of articles of paperboard", "printing_and_service_activities_related_to_printing": "Printing and service activities related to printing", "reproduction_of_recorded_media": "Reproduction of recorded media", "manufacture_of_coke_oven_products": "Manufacture of coke oven products", "manufacture_of_refined_petroleum_products": "Manufacture of refined petroleum products", "manufacture_of_basic_chemicals_fertilizers_and_nitrogen_compounds_plastics_and_synthetic_rubber": "Manufacture of basic chemicals, fertilizers and nitrogen compounds, plastics and synthetic rubber in primary forms", "manufacture_of_pesticides_and_other_agrochemical_products": "Manufacture of pesticides and other agrochemical products", "manufacture_of_paints_varnishes_printing_ink_and_mastics": "Manufacture of paints, varnishes and similar coatings, printing ink and mastics", "manufacture_of_soap_detergents_cleaning_polishing_perfumes_and_toilet_preparations": "Manufacture of soap and detergents, cleaning and polishing preparations, perfumes and toilet preparations", "manufacture_of_other_chemical_products": "Manufacture of other chemical products", "manufacture_of_man_made_fibres": "Manufacture of man-made fibres", "manufacture_of_basic_pharmaceutical_products_and_preparations": "Manufacture of basic pharmaceutical products and pharmaceutical preparations", "manufacture_of_rubber_tyres_and_tubes_retreading_and_rebuilding": "Manufacture of rubber tyres and tubes retreading and rebuilding of rubber tyres", "manufacture_of_other_rubber_products": "Manufacture of other rubber products (plastic plates and tubes, etc)", "manufacture_of_plastic_products": "Manufacture of plastic products", "manufacture_of_glass_and_glass_products": "Manufacture of glass and glass products", "manufacture_of_refractory_products": "Manufacture of refractory products", "manufacture_of_clay_building_materials": "Manufacture of clay building materials", "manufacture_of_other_porcelain_and_ceramic_products": "Manufacture of other porcelain and ceramic products", "manufacture_of_cement_lime_and_plaster": "Manufacture of cement and lime and plaster", "manufacture_of_articles_of_concrete_cement_and_plaster": "Manufacture of articles of concrete, cement and plaster", "cutting_shaping_and_finishing_of_stone": "Cutting, shaping and finishing of stone", "manufacture_of_abrasive_products_and_non_metallic_mineral_products_nec": "Manufacture of abrasive products and non-metallic mineral products not elsewhere classified", "manufacture_of_basic_iron_and_steel_and_ferro_alloys": "Manufacture of basic iron and steel and of ferro-alloys", "manufacture_of_tubes_pipes_hollow_profiles_and_fittings_of_steel": "Manufacture of tubes and pipes and hollow profiles and related fittings of steel", "manufacture_of_other_products_and_first_processing_of_steel": "Manufacture of other products and first processing of steel", "manufacture_of_basic_precious_and_other_non_ferrous_metals": "Manufacture of basic precious and other non-ferrous metals (e.g aluminium, lead, zinc and tin, copper etc.)", "processing_of_nuclear_fuel": "Processing of nuclear fuel", "casting_of_metals": "Casting of metals (iron, steel, light metals, non-ferrous metals)", "manufacture_of_structural_metal_products": "Manufacture of structural metal products", "manufacture_of_tanks_reservoirs_and_containers_of_metal": "Manufacture of tanks, reservoirs and containers of metal manufacture of central heating radiators and boilers other tanks, reservoirs and containers of metal)", "manufacture_of_steam_generators_except_central_heating_boilers": "Manufacture of steam generators, except central heating hot water boilers", "manufacture_of_weapons_and_ammunition": "Manufacture of weapons and ammunition", "forging_pressing_stamping_and_roll_forming_of_metal_powder_metallurgy": "Forging, pressing, stamping and roll-forming of metal powder metallurgy", "treatment_and_coating_of_metals": "Treatment and coating of metals", "machining": "Machining", "manufacture_of_cutlery": "Manufacture of cutlery", "manufacture_of_locks_and_hinges": "Manufacture of locks and hinges", "manufacture_of_tools": "Manufacture of tools", "manufacture_of_other_fabricated_metal_products": "Manufacture of other fabricated metal products", "manufacture_of_electronic_components_and_boards": "Manufacture of electronic components and boards", "manufacture_of_computers_and_peripheral_equipment": "Manufacture of computers and peripheral equipment", "manufacture_of_communication_equipment": "Manufacture of communication equipment", "manufacture_of_consumer_electronics": "Manufacture of consumer electronics", "manufacture_of_instruments_and_appliances_for_measuring_testing_and_navigation": "Manufacture of instruments and appliances for measuring, testing and navigation watches and clocks", "manufacture_of_irradiation_electromedical_and_electrotherapeutic_equipment": "Manufacture of irradiation, electromedical and electrotherapeutic equipment", "manufacture_of_optical_instruments_and_photographic_equipment": "Manufacture of optical instruments and photographic equipment", "manufacture_of_magnetic_and_optical_media": "Manufacture of magnetic and optical media", "manufacture_of_electric_motors_generators_transformers_and_electricity_distribution": "Manufacture of electric motors, generators, transformers and electricity distribution and control apparatus", "manufacture_of_batteries_and_accumulators": "Manufacture of batteries and accumulators", "manufacture_of_wiring_and_wiring_devices": "Manufacture of wiring and wiring devices", "manufacture_of_electric_lighting_equipment": "Manufacture of electric lighting equipment", "manufacture_of_domestic_appliances": "Manufacture of domestic appliances", "manufacture_of_other_electrical_equipment": "Manufacture of other electrical equipment", "manufacture_of_general_purpose_machinery": "Manufacture of general – purpose machinery", "manufacture_of_ovens_furnaces_and_furnace_burners": "Manufacture of ovens, furnaces and furnace burners", "manufacture_of_lifting_and_handling_equipment": "Manufacture of lifting and handling equipment", "manufacture_of_office_machinery_and_equipment": "Manufacture of office machinery and equipment (except computers and peripheral equipment)", "manufacture_of_power_driven_hand_tools": "Manufacture of power-driven hand tools", "manufacture_of_non_domestic_cooling_and_ventilation_equipment": "Manufacture of non-domestic cooling and ventilation equipment", "manufacture_of_other_general_purpose_machinery_nec": "Manufacture of other general-purpose machinery not elsewhere classified", "manufacture_of_agricultural_forestry_machinery": "Manufacture of agricultural forestry machinery", "manufacture_of_metal_forming_machinery_and_machine_tools": "Manufacture of metal forming machinery and machine tools", "manufacture_of_other_special_purpose_machinery": "Manufacture of other special purpose machinery", "manufacture_of_motor_vehicles": "Manufacture of motor vehicles", "manufacture_of_bodies_for_motor_vehicles_trailers_and_semi_trailers": "Manufacture of bodies (coachwork) for motor vehicles manufacture of trailers and semi-trailers", "manufacture_of_parts_and_accessories_for_motor_vehicles": "Manufacture of parts and accessories for motor vehicles", "building_of_ships_and_floating_structures": "Building of ships and floating structures", "building_of_pleasure_and_sporting_boats": "Building of pleasure and sporting boats", "manufacture_of_railway_locomotives_and_rolling_stock": "Manufacture of railway locomotives and rolling stock", "manufacture_of_air_and_spacecraft_and_related_machinery": "Manufacture of air and spacecraft and related machinery", "manufacture_of_military_fighting_vehicles_icbm": "Manufacture of military fighting vehicles, intercontinental ballistic missiles (ICBM)", "manufacture_of_transport_equipment_nec": "Manufacture of transport equipment n.e.c. (e.g. motorcycles, bicycles and invalid carriages)", "manufacture_of_furniture": "Manufacture of furniture", "manufacturing_of_jewellery_bijouteries_and_related_articles": "Manufacturing of jewellery, bijouteries and related articles", "manufacture_of_musical_instruments": "Manufacture of musical instruments", "manufacture_of_sports_goods": "Manufacture of sports goods", "manufacture_of_games_and_toys": "Manufacture of games and toys", "manufacture_of_medical_and_dental_instruments_and_supplies": "Manufacture of medical and dental instruments and supplies", "manufacturing_nec": "Manufacturing n.e.c. (brooms and brushes, protective safety equipment and clothing, pens and pencils, stamps, labels, buttons, articles of personal or household use, candles, artificial flowers, fruit and foliage, floral baskets, etc.)", "repair_of_fabricated_metal_products_machinery_and_equipment": "Repair of fabricated metal products, machinery and equipment", "repair_of_electrical_equipment": "Repair of electrical equipment", "repair_and_maintenance_of_ships_and_boats": "Repair and maintenance of ships and boats", "repair_and_maintenance_of_aircraft_and_spacecraft": "Repair and maintenance of aircraft and spacecraft", "repair_and_maintenance_of_other_transport_equipment": "Repair and maintenance of other transport equipment", "installation_of_industrial_machinery_and_equipment": "Installation of industrial machinery and equipment", "d_electricity_gas_steam_and_air_conditioning_supply": "D Electricity, gas steam and air conditioning supply", "electric_power_generation_transmission_and_distribution": "Electric power generations and transmission and distribution", "production_of_electricity": "Production of electricity", "transmission_of_electricity": "Transmission of electricity", "distribution_of_electricity": "Distribution of electricity", "trade_of_electricity": "Trade of electricity", "manufacture_of_gas_distribution_of_gaseous_fuels": "Manufacture of gas distribution of gaseous fuels through mains", "distribution_of_gaseous_fuels_through_mains": "Distribution of gaseous fuels through mains", "trade_of_gas_through_mains": "Trade of gas through mains", "steam_and_air_conditioning_supply": "Steam and air conditioning supply", "e_water_supply_sewerage_waste_management_and_remediation": "E Water supply (sewerage, waste management and remediation activities)", "water_collection_treatment_and_supply": "Water collection and treatment and supply", "sewerage": "Sewerage", "collection_of_non_hazardous_waste": "Collection of non-hazardous waste", "collection_of_hazardous_waste": "Collection of hazardous waste", "treatment_and_disposal_of_non_hazardous_waste": "Treatment and disposal of non-hazardous waste", "treatment_and_disposal_of_hazardous_waste": "Treatment and disposal of hazardous waste", "dismantling_of_wrecks": "Dismantling of wrecks", "recovery_of_sorted_materials": "Recovery of sorted materials", "remediation_activities_and_other_waste_management_services": "Remediation activities and other waste management services", "f_construction": "F Construction", "development_of_building_projects": "Development of building projects", "construction_of_residential_and_non_residential_buildings": "Construction of residential and non-residential buildings", "construction_of_roads_and_railways": "Construction of roads and railways", "construction_of_utility_projects": "Construction of utility projects (for fluids, electricity and telecommunications)", "construction_of_other_civil_engineering_projects": "Construction of other civil engineering projects (water projects and industrial facilities)", "demolition_and_site_preparation": "Demolition and site preparation (demolition, site preparation, test drilling and boring)", "electrical_plumbing_and_other_construction_installation": "Electrical and plumbing and other construction installation activities", "building_completion_and_finishing": "Building completion and finishing (plastering joinery installation, floor and wall covering, painting and glazing, other building completion and finishing)", "other_specialised_construction_activities": "Other specialised construction activities", "g_wholesale_and_retail_trade_repair_of_motor_vehicles": "G Wholesale and retail trade and repair of motor vehicles and motorcycles", "sale_of_motor_vehicles": "Sale of motor vehicles", "maintenance_and_repair_of_motor_vehicles": "Maintenance and repair of motor vehicles", "sale_of_motor_vehicle_parts_and_accessories": "Sale of motor vehicle parts and accessories", "sale_maintenance_and_repair_of_motorcycles": "Sale, maintenance and repair of motorcycles and related parts and accessories", "wholesale_on_a_fee_or_contract_basis": "Wholesale on a fee or contract basis", "wholesale_of_grain_unmanufactured_tobacco_seeds_and_animal_feeds": "Wholesale of grain, unmanufactured tobacco, seed and animal feeds", "wholesale_of_flowers_and_plants": "Wholesale of flowers and plants", "wholesale_of_live_animals": "Wholesale of live animals", "wholesale_of_hides_skins_and_leather": "Wholesale of hides, skins and leather", "wholesale_of_food_beverages_and_tobacco": "Wholesale of food, beverages and tobacco", "wholesale_of_tobacco_products": "Wholesale of tobacco products", "wholesale_of_household_goods": "Wholesale of household goods", "wholesale_of_information_and_communication_equipment": "Wholesale of information and communication equipment", "wholesale_of_other_machinery_equipment_and_supplies": "Wholesale of other machinery, equipment and supplies", "wholesale_of_solid_liquid_and_gaseous_fuels": "Wholesale of solid, liquid and gaseous fuels and related products", "wholesale_of_metals_and_metal_ores": "Wholesale of metals and metal ores", "wholesale_of_wood_construction_materials_and_sanitary_equipment": "Wholesale of wood, construction materials and sanitary equipment", "wholesale_of_hardware_plumbing_and_heating_equipment": "Wholesale of hardware, plumbing and heating equipment and supplies", "wholesale_of_chemical_products": "Wholesale of chemical products", "wholesale_of_other_intermediate_products": "Wholesale of other intermediate products", "wholesale_of_waste_and_scrap": "Wholesale of waste and scrap", "non_specialised_wholesale_trade": "Non-specialised wholesale trade", "retail_sale_in_non_specialised_stores_food_beverages_tobacco": "Retail sale in non-specialised stores with food, beverages or tobacco predominating", "other_retail_sale_in_non_specialised_stores": "Other retail sale in non-specialised stores", "retail_sale_of_food_beverages_and_tobacco_specialised": "Retail sale of food, beverages and tobacco in specialised stores", "retail_sale_of_tobacco_products_specialised": "Retail sale of tobacco products in specialised stores", "retail_sale_of_automotive_fuel_specialised": "Retail sale of automotive fuel in specialised stores", "retail_sale_of_information_and_communication_equipment_specialised": "Retail sale of information and communication equipment in specialised stores", "retail_sale_of_other_household_equipment_specialised": "Retail sale of other household equipment in specialised stores", "retail_sale_of_cultural_and_recreation_goods_specialised": "Retail sale of cultural and recreation goods in specialised stores", "retail_sale_of_other_goods_specialised": "Retail sale of other goods in specialised stores", "dispensing_chemist_specialised": "Dispensing chemist in specialised stores", "retail_sale_of_medical_and_orthopaedic_goods_specialised": "Retail sale of medical and orthopaedic goods in specialised stores", "retail_sale_of_flowers_plants_seeds_fertilisers_pets_specialised": "Retail sale of flowers, plants, seeds, fertilisers, pet animals and pet food in specialised stores", "retail_sale_via_stalls_and_markets": "Retail sale via stalls and markets", "retail_trade_not_in_stores_stalls_or_markets": "Retail trade not in stores, stalls or markets", "h_transportation_and_storage": "H Transportation and Storage", "passenger_rail_transport_interurban": "Passenger rail transport and interurban", "freight_rail_transport": "Freight rail transport", "other_passenger_land_transport": "Other passenger land transport", "freight_transport_by_road_and_removal_services": "Freight transport by road and removal services", "transport_via_pipeline": "Transport via pipeline", "sea_and_coastal_passenger_water_transport": "Sea and coastal passenger water transport", "sea_and_coastal_freight_water_transport": "Sea and coastal freight water transport", "inland_passenger_water_transport": "Inland passenger water transport", "inland_freight_water_transport": "Inland freight water transport", "passenger_air_transport": "Passenger air transport", "freight_air_transport_and_space_transport": "Freight air transport and space transport", "warehousing_and_storage": "Warehousing and storage", "support_activities_for_transportation": "Support activities for transportation", "postal_activities_under_universal_service_obligation": "Postal activities under universal service obligation", "other_postal_and_courier_activities": "Other postal and courier activities", "i_accommodation_and_food_service_activities": "I Accommodation and food service activities", "hotels_and_similar_accommodation": "Hotels and similar accommodation", "holiday_and_other_short_stay_accommodation": "Holiday and other short-stay accommodation", "camping_grounds_recreational_vehicle_parks_and_trailer_parks": "Camping grounds, recreational vehicle parks and trailer parks", "other_accommodation": "Other accommodation", "restaurants_and_mobile_food_service_activities": "Restaurants and mobile food service activities", "event_catering_and_other_food_service_activities": "Event catering and other food service activities", "beverage_serving_activities": "Beverage serving activities", "j_information_and_communication": "J Information and communication", "publishing_of_books_periodicals_and_other_publishing": "Publishing of books, periodicals and other publishing activities", "software_publishing": "Software publishing", "motion_picture_video_and_television_programme_activities": "Motion picture, video and television programme activities", "sound_recording_and_music_publishing": "Sound recording and music publishing activities", "radio_broadcasting": "Radio broadcasting", "television_programming_and_broadcasting": "Television programming and broadcasting activities", "wired_telecommunications_activities": "Wired telecommunications activities", "wireless_telecommunications_activities": "Wireless telecommunications activities", "satellite_telecommunications_activities": "Satellite telecommunications activities", "other_telecommunications_activities": "Other telecommunications activities", "computer_programming_consultancy_and_related_activities": "Computer programming, consultancy and related activities", "data_processing_hosting_and_related_activities_web_portals": "Data processing, hosting and related activities and web portals", "other_information_service_activities": "Other information service activities", "k_financial_and_insurance_activities": "K Financial and insurance activities", "monetary_intermediation": "Monetary intermediation", "activities_of_holding_companies": "Activities of holding companies", "trusts_funds_and_similar_financial_entities": "Trusts, funds and similar financial entities", "other_financial_service_activities_except_insurance_and_pension": "Other financial service activities, except insurance and pension funding", "insurance": "Insurance", "reinsurance": "Reinsurance", "pension_funding": "Pension funding", "activities_auxiliary_to_financial_services_except_insurance": "Activities auxiliary to financial services, except insurance and pension funding", "activities_auxiliary_to_insurance_and_pension_funding": "Activities auxiliary to insurance and pension funding", "fund_management_activities": "Fund management activities", "l_real_estate_activities": "L Real estate activities", "buying_and_selling_of_own_real_estate": "Buying and selling of own real estate", "renting_and_operating_of_own_or_leased_real_estate": "Renting and operating of own or leased real estate", "real_estate_agencies": "Real estate agencies", "management_of_real_estate_on_fee_or_contract_basis": "Management of real estate on a fee or contract basis", "m_professional_scientific_and_technical_activities": "M Professional, scientific and technical activities", "legal_activities": "Legal activities", "accounting_bookkeeping_auditing_and_tax_consultancy": "Accounting, bookkeeping and auditing activities and tax consultancy", "activities_of_head_offices": "Activities of head offices", "management_consultancy_activities": "Management consultancy activities", "architectural_and_engineering_activities_and_technical_consultancy": "Architectural and engineering activities and related technical consultancy", "technical_testing_and_analysis": "Technical testing and analysis", "research_and_development_on_natural_sciences_and_engineering": "Research and experimental development on natural sciences and engineering", "research_and_development_on_social_sciences_and_humanities": "Research and experimental development on social sciences and humanities", "advertising": "Advertising", "market_research_and_public_opinion_polling": "Market research and public opinion polling", "specialised_design_activities": "Specialised design activities", "photographic_activities": "Photographic activities", "translation_and_interpretation_activities": "Translation and interpretation activities", "other_professional_scientific_and_technical_activities_nec": "Other professional and scientific and technical activities n.e.c.", "veterinary_activities": "Veterinary activities", "n_administrative_and_support_service_activities": "N Administrative and support service activities", "renting_and_leasing_of_motor_vehicles": "Renting and leasing of motor vehicles", "renting_and_leasing_of_personal_and_household_goods": "Renting and leasing of personal and household goods", "renting_and_leasing_of_recreational_and_sports_goods": "Renting and leasing of recreational and sports goods", "renting_of_video_tapes_and_disks": "Renting of video tapes and disks", "renting_and_leasing_of_other_machinery_equipment_and_goods": "Renting and leasing of other machinery, equipment and tangible goods", "activities_of_employment_placement_agencies": "Activities of employment placement agencies", "temporary_employment_agencies": "Temporary employment agencies", "other_human_resources_provision": "Other human resources provision", "travel_agency_and_tour_operator_activities": "Travel agency and tour operator activities", "other_reservation_service_and_related_activities": "Other reservation service and related activities", "private_security_activities": "Private security activities", "security_systems_service_activities": "Security systems service activities", "investigation_activities": "Investigation activities", "combined_facilities_support_activities": "Combined facilities support activities", "general_cleaning_of_buildings": "General Cleaning of buildings", "other_buildings_and_industrial_cleaning_activities": "Other buildings and industrial cleaning activities", "other_cleaning_activities": "Other cleaning activities", "landscape_service_activities": "Landscape service activities", "combined_office_administrative_service_activities": "Combined office administrative service activities", "photocopying_document_preparation_and_office_support": "Photocopying, document preparation and other specialised office support activities", "activities_of_call_centres": "Activities of call centres", "organisation_of_conventions_and_trade_shows": "Organisation of conventions and trade shows", "activities_of_collection_agencies_and_credit_bureaus": "Activities of collection agencies and credit bureaus", "packaging_activities": "Packaging activities", "other_business_support_service_activities_nec": "Other business support service activities not elsewhere classified", "o_public_administration_and_defence_social_security": "O Public administration and defence compulsory social security", "administration_of_the_state_and_economic_social_policy": "Administration of the State and the economic and social policy of the community", "provision_of_services_to_the_community": "Provision of services to the community as a whole", "compulsory_social_security_activities": "Compulsory social security activities", "p_education": "P Education", "other_education": "Other education", "educational_support_activities": "Educational support activities", "q_human_health_and_social_work_activities": "Q Human health and social work activities", "hospital_activities": "Hospital activities", "medical_and_dental_practice_activities": "Medical and dental practice activities", "other_human_health_activities": "Other human health activities", "residential_nursing_care_activities": "Residential nursing care activities", "residential_care_activities_for_mental_health_and_substance_abuse": "Residential care activities for mental retardation, mental health and substance abuse", "residential_care_activities_for_the_elderly_and_disabled": "Residential care activities for the elderly and disabled", "other_residential_care_activities": "Other residential care activities", "social_work_activities_without_accommodation_for_elderly": "Social work activities without accommodation for the elderly and disabled", "other_social_work_activities_without_accommodation": "Other social work activities without accommodation", "r_arts_entertainment_and_recreation": "R Arts entertainment and recreation", "creative_arts_and_entertainment_activities": "Creative and arts and entertainment activities", "gambling_and_betting_activities": "Gambling and betting activities", "amusement_and_recreation_activities": "Amusement and recreation activities", "s_other_service_activities": "S Other service activities", "activities_of_business_employers_and_professional_organisations": "Activities of business and employers and professional membership organisations", "activities_of_trade_unions": "Activities of trade unions", "activities_of_other_membership_organisations": "Activities of other membership organisations", "repair_of_computers_and_communication_equipment": "Repair of computers and communication equipment", "repair_of_personal_and_household_goods": "Repair of personal and household goods", "washing_and_drycleaning_of_textile_and_fur_products": "Washing and drycleaning of textile and fur products", "hairdressing_and_other_beauty_treatment": "Hairdressing and other beauty treatment", "funeral_and_related_activities": "Funeral and related activities", "total_ghg_emissions": "Total GHG emissions", "total_scope_1_&_scope_2": "Total Scope 1 & <PERSON>ope 2", "scope_2_(location_based)": "Scope 2 (location based)", "scope_2_(market_based)": "Scope 2 (market based)", "scope_3": "Scope 3"}, "surveyForm": {"title": "UNIFIED CLIENT QUESTIONNAIRE ON ENVIRONMENTAL AND CLIMATE-RELATED DISCLOSURES", "introduction": {"title": "Introduction", "main_text": {"title": "", "description": "Following regulatory requirements (i.e. the Non-Financial Reporting Directive (NFRD) and upcoming Corporate Sustainability Reporting Directive (CSRD)) as for reporting purposes, this unified questionnaire is designed by Estonian, Latvian and Lithuanian Banking Associations to collect quantitative and qualitative data to assess Client's environmental and climate-related impact, social and governance practices, as well as to evaluate Client's readiness to green transition."}}, "general": {"title": "General", "general_questionnaire_completion_date": {"title": "Questionnaire Completion Date", "description": "The date on which the questionnaire is completed, establishing the reporting period for the data provided.", "placeholder": "Enter date"}, "general_company_name": {"title": "Company Name", "description": "The legal name of the company used for identification and regulatory reporting.", "placeholder": "Enter company name"}, "general_company_registration_number": {"title": "Company Registration Number", "description": "The unique identifier Assigned to the company by the relevant registration authority.", "placeholder": "Enter company registration number"}, "general_company_indicators": {"title": "Company Indicators", "description": "Please provide information on the following Company indicators for the last full financial year", "helpText": "This section header introduces key company indicators for the last full financial year.", "general_company_indicators_financial_year": {"title": "Financial Year", "description": "The fiscal year under review during which the company’s financial and operational performance is measured.", "placeholder": "Enter financial year"}, "general_company_indicators_net_turnover": {"title": "Net Turnover (EUR)", "description": "The total revenue generated by the company during the financial year, expressed in euros.", "placeholder": "Enter net turnover"}, "general_company_indicators_total_assets": {"title": "Total Assets (EUR)", "description": "The total value of the company’s assets as of the end of the reporting period, measured in euros.", "placeholder": "Enter total assets"}, "general_company_indicators_number_of_employees": {"title": "Number of Employees", "description": "The number of employees working for the company during the reporting period.", "placeholder": "Enter number of employees"}}, "general_economic_activities": {"title": "Main Economic Activities", "description": "Please indicate up to three main economic activities for the company and their share in the last full financial year", "helpText": "This section requests the disclosure of up to three main economic activities and their respective contributions to net turnover.", "addItemLabel": "Economic Activity", "items": {"general_economic_activities_activity": {"title": "Add Economic Activity", "description": "Details of a primary economic activity carried out by the company.", "placeholder": "Select activity"}, "general_economic_activities_industry_category": {"title": "Industry (Category)", "description": "The broad industry classification in which the economic activity is categorized.", "placeholder": "Category"}, "general_economic_activities_industry_sector": {"title": "Industry (Sector)", "description": "A more detailed industry section classification for the economic activity.", "placeholder": "Sector"}, "general_economic_activities_share_in_net_turnover": {"title": "Share in net turnover (%)", "description": "The percentage share of the company’s net turnover attributable to this economic activity.", "placeholder": "Enter %"}}}, "general_revenue_questions": {"title": "Revenue Information", "general_revenue_questions_coal_revenue": {"title": "Does the Company derive 1 % or more of their revenues from the exploration, mining, extraction, distribution or refining of hard coal and lignite?", "description": "Indicates whether 1% or more of the company’s revenue is derived from hard coal and lignite-related activities, which is relevant for assessing fossil fuel dependency.", "placeholder": "Answer"}, "general_revenue_questions_oil_revenue": {"title": "Does the Company derive 10 % or more of their revenues from the exploration, extraction, distribution or refining of oil fuels?", "description": "Assesses if 10% or more of the company’s revenue comes from oil fuels, highlighting exposure to fossil fuel activities.", "placeholder": "Answer"}, "general_revenue_questions_gaseous_fuels_revenue": {"title": "Does the Company derive 50 % or more of their revenues from the exploration, extraction, manufacturing or distribution of gaseous fuels?", "description": "Evaluates whether 50% or more of the company’s revenue is generated from gaseous fuels, indicating significant reliance on these energy sources.", "placeholder": "Answer"}, "general_revenue_questions_electricity_generation_revenue": {"title": "Does the Company derive 50 % or more of their revenues from electricity generation with a GHG intensity of more than 100 g CO2 e/kWh?", "description": "Determines if at least 50% of the company’s revenue comes from electricity generation with high GHG intensity, critical for climate impact assessments.", "placeholder": "Answer"}}, "general_is_pie_company": {"title": "Is the company a PIE?", "description": "Determines if the company is a Public Interest Entity (PIE).", "placeholder": "Answer"}, "general_properties_information": {"title": "Company Properties", "general_properties_information_number_of_properties": {"title": "How many properties and/or locations are associated with the Company's operations?", "description": "Specifies the total number of physical properties or operational locations associated with the company.", "placeholder": "Enter number of locations"}, "general_properties_information_can_define_main_properties": {"title": "Is it possible to define up to 5 main properties/locations?", "description": "*Main Real Estate object - real estate object (leased or owned), which is critically important to ensure business activity or represents significant share of the assets of the Company. Please indicate up to 5 main real estate objects.\nIf Company operates more than 5 real estate objects of equal significance, please fill in by providing general information on number, type and location of Group of real estate objects.\n**Group of Real estate objects - objects with identical purpose of use located in the same city/parish/district", "helpText": "Whether it is possible to define up to 5 main properties/locations.", "placeholder": "Answer"}, "general_properties_information_main_properties": {"title": "Main Real Estate Locations", "description": "Please indicate up to 5 main real estate locations, which are critically important for the business activity of the Company.", "helpText": "Requests detailed location information (country, address, city, state, postal code) for each property associated with the company.", "addItemLabel": "Location", "items": {"general_properties_information_main_properties_purpose_of_use": {"title": "Purpose of use", "description": "Indicates the intended purpose or use of the first listed address, based on predefined usage categories.", "placeholder": "purpose"}, "general_properties_information_main_properties_country": {"title": "Country", "description": "Specifies the country where the first property is located.", "placeholder": "Enter Country"}, "general_properties_information_main_properties_street_address": {"title": "Street Address", "description": "Provides the street address and building identifier or property name for the first property.", "placeholder": "Enter Street Address"}, "general_properties_information_main_properties_city_county_state": {"title": "City/County/State", "description": "Identifies the city, county, or state for the first property location.", "placeholder": "Enter City/County/State"}, "general_properties_information_main_properties_postal_code": {"title": "Postal code", "description": "Specifies the postal code corresponding to the first property location.", "placeholder": "Enter Postal code"}}}}}, "climate_impact": {"title": "Climate Impact", "description": "Climate change and environmental degradation is a threat, which puts preasure on social, economic and business environment. GHG emissions are the primary driver of global climate change, while energy consumption (in particular burning of fossil fuels), is the main source of GHG emissions. To overcome climate change challenges European Union is taking action by setting ambitious goal of GHG reduction by 55% by 2030 and by becomming climate neutral by 2050. In order to achieve it, every action and step towards lower GHG emission economy is important.", "climate_impact_ghg_emissions": {"title": "GHG Emissions", "climate_impact_ghg_emissions_monitors_ghg_emissions": {"title": "Does the Company monitor and/or calculate GHG emissions?", "description": "Indicates whether the company actively monitors and calculates its greenhouse gas (GHG) emissions—a critical element of climate reporting.", "placeholder": "Answer"}, "climate_impact_ghg_emissions_emission_volumes": {"title": "Please provide information, (where available), on the Company's GHG emission volume for the last 3 calendar years:", "description": "Requests the disclosure of annual GHG emission volumes for the last three calendar years if monitoring is conducted.", "climate_impact_ghg_emissions_emission_volumes_scope_1": {"title": "GHG emissions scope 1, (tCO2eq)", "description": "Reports direct GHG emissions (Scope 1) measured in tonnes of CO2 equivalent.", "climate_impact_ghg_emissions_emission_volumes_scope_1_year_2023": {"placeholder": "Enter volume"}, "climate_impact_ghg_emissions_emission_volumes_scope_1_year_2022": {"placeholder": "Enter volume"}, "climate_impact_ghg_emissions_emission_volumes_scope_1_year_2021": {"placeholder": "Enter volume"}}, "climate_impact_ghg_emissions_emission_volumes_scope_2": {"title": "GHG emissions scope 2, (tCO2eq)", "description": "Reports indirect GHG emissions from purchased energy (Scope 2) in tonnes of CO2 equivalent.", "climate_impact_ghg_emissions_emission_volumes_scope_2_year_2023": {"placeholder": "Enter volume"}, "climate_impact_ghg_emissions_emission_volumes_scope_2_year_2022": {"placeholder": "Enter volume"}, "climate_impact_ghg_emissions_emission_volumes_scope_2_year_2021": {"placeholder": "Enter volume"}}, "climate_impact_ghg_emissions_emission_volumes_scope_3": {"title": "GHG emissions scope 3, (tCO2eq), whereof:", "description": "Reports other indirect GHG emissions (Scope 3) associated with the company’s value chain, measured in tonnes of CO2 equivalent.", "climate_impact_ghg_emissions_emission_volumes_scope_3_purchased_goods": {"title": "Purchased goods and services", "description": "Specifies GHG emissions related to purchased goods and services, which are part of Scope 3 emissions.", "placeholder": "Enter volume", "climate_impact_ghg_emissions_emission_volumes_scope_3_purchased_goods_year_2023": {"placeholder": "Enter volume"}, "climate_impact_ghg_emissions_emission_volumes_scope_3_purchased_goods_year_2022": {"placeholder": "Enter volume"}, "climate_impact_ghg_emissions_emission_volumes_scope_3_purchased_goods_year_2021": {"placeholder": "Enter volume"}}, "climate_impact_ghg_emissions_emission_volumes_scope_3_business_travel": {"title": "Business travel", "description": "Reports GHG emissions resulting from business travel, included under Scope 3.", "placeholder": "Enter volume", "climate_impact_ghg_emissions_emission_volumes_scope_3_business_travel_year_2023": {"placeholder": "Enter volume"}, "climate_impact_ghg_emissions_emission_volumes_scope_3_business_travel_year_2022": {"placeholder": "Enter volume"}, "climate_impact_ghg_emissions_emission_volumes_scope_3_business_travel_year_2021": {"placeholder": "Enter volume"}}, "climate_impact_ghg_emissions_emission_volumes_scope_3_employee_commuting": {"title": "Employee commuting", "description": "Details GHG emissions generated from employee commuting, as part of Scope 3.", "placeholder": "Enter volume", "climate_impact_ghg_emissions_emission_volumes_scope_3_employee_commuting_year_2023": {"placeholder": "Enter volume"}, "climate_impact_ghg_emissions_emission_volumes_scope_3_employee_commuting_year_2022": {"placeholder": "Enter volume"}, "climate_impact_ghg_emissions_emission_volumes_scope_3_employee_commuting_year_2021": {"placeholder": "Enter volume"}}, "climate_impact_ghg_emissions_emission_volumes_scope_3_leased_assets": {"title": "Leased assets", "description": "Reports GHG emissions associated with leased assets, included in Scope 3.", "placeholder": "Enter volume", "climate_impact_ghg_emissions_emission_volumes_scope_3_leased_assets_year_2023": {"placeholder": "Enter volume"}, "climate_impact_ghg_emissions_emission_volumes_scope_3_leased_assets_year_2022": {"placeholder": "Enter volume"}, "climate_impact_ghg_emissions_emission_volumes_scope_3_leased_assets_year_2021": {"placeholder": "Enter volume"}}, "climate_impact_ghg_emissions_emission_volumes_scope_3_transportation_services": {"title": "Transport Services", "description": "Specifies GHG emissions from transportation and distribution services, reported under Scope 3.", "placeholder": "Enter volume", "climate_impact_ghg_emissions_emission_volumes_scope_3_transportation_services_year_2023": {"placeholder": "Enter volume"}, "climate_impact_ghg_emissions_emission_volumes_scope_3_transportation_services_year_2022": {"placeholder": "Enter volume"}, "climate_impact_ghg_emissions_emission_volumes_scope_3_transportation_services_year_2021": {"placeholder": "Enter volume"}}, "climate_impact_ghg_emissions_emission_volumes_scope_3_investments": {"title": "Investments", "description": "Reports GHG emissions attributed to investments, included as part of Scope 3.", "placeholder": "Enter volume", "climate_impact_ghg_emissions_emission_volumes_scope_3_investments_year_2023": {"placeholder": "Enter volume"}, "climate_impact_ghg_emissions_emission_volumes_scope_3_investments_year_2022": {"placeholder": "Enter volume"}, "climate_impact_ghg_emissions_emission_volumes_scope_3_investments_year_2021": {"placeholder": "Enter volume"}}, "climate_impact_ghg_emissions_emission_volumes_scope_3_other": {"title": "Other", "description": "Covers other categories of Scope 3 GHG emissions not captured in the previous items.", "placeholder": "Enter volume", "climate_impact_ghg_emissions_emission_volumes_scope_3_other_year_2023": {"placeholder": "Enter volume"}, "climate_impact_ghg_emissions_emission_volumes_scope_3_other_year_2022": {"placeholder": "Enter volume"}, "climate_impact_ghg_emissions_emission_volumes_scope_3_other_year_2021": {"placeholder": "Enter volume"}}}}, "climate_impact_ghg_emissions_calculation_framework": {"title": "What framework is used for the calculation of GHG emissions?", "description": "Identifies the framework or methodology (e.g., GHG Protocol, ISO 14064) used for calculating the company’s GHG emissions.", "placeholder": "framework"}, "climate_impact_ghg_emissions_external_validation": {"title": "Is external validation/verification available for the GHG emissions calculation?", "description": "Indicates whether the company’s GHG emissions data has undergone external validation or verification.", "placeholder": "Answer"}, "climate_impact_ghg_emissions_verifier": {"title": "If yes, please disclose the verifier:", "description": "Provides the name or details of the external verifier if the GHG emissions data has been validated.", "placeholder": "verifier"}, "climate_impact_ghg_emissions_reduction_plan": {"title": "Does the Company have a GHG emission reduction plan?", "description": "Indicates whether the company has a plan in place to reduce its GHG emissions.", "placeholder": "Answer"}, "climate_impact_ghg_emissions_reduction_goals": {"title": "Please indicate your GHG emissions reduction goals:", "description": "Details the GHG emission reduction goals, including short-, medium-, and long-term targets, along with baseline and target years where applicable.", "addItemLabel": "Reduction Goal", "items": {"climate_impact_ghg_emissions_reduction_goals_reduction_goal": {"title": "Reduction Goal", "placeholder": "Enter reduction goal"}, "climate_impact_ghg_emissions_reduction_goals_metrics": {"title": "Metrics", "placeholder": "Select metrics"}, "climate_impact_ghg_emissions_reduction_goals_scope": {"title": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON>"}, "climate_impact_ghg_emissions_reduction_goals_baseline_year": {"title": "Baseline Year", "placeholder": "Enter baseline year"}, "climate_impact_ghg_emissions_reduction_goals_target_year": {"title": "Target Year", "placeholder": "Enter target year"}}}}, "climate_impact_energy_consumption": {"title": "Energy consumption", "climate_impact_energy_consumption_non_renewable_sources": {"title": "Check all types of NON-RENEWABLE sources of energy consumed by the Company for the last full calendar year", "description": "Requests details on the consumption of non-renewable energy sources for the last full calendar year, including volume and metric for each type.", "addItemLabel": "Add Other Source", "climate_impact_energy_consumption_non_renewable_sources_district_heating": {"title": "District Heating", "description": "Indicates whether district heating is consumed as a non-renewable energy source, including corresponding volume metrics.", "climate_impact_energy_consumption_non_renewable_sources_district_heating_enabled": {"title": "District Heating"}, "climate_impact_energy_consumption_non_renewable_sources_district_heating_volume": {"title": "Volume", "placeholder": "Enter volume"}, "climate_impact_energy_consumption_non_renewable_sources_district_heating_metrics": {"title": "Indicate metrics", "placeholder": "metric"}}, "climate_impact_energy_consumption_non_renewable_sources_gas": {"title": "Gas", "description": "Specifies the consumption of gas as a non-renewable energy source, with volume details provided.", "climate_impact_energy_consumption_non_renewable_sources_gas_enabled": {"title": "Gas"}, "climate_impact_energy_consumption_non_renewable_sources_gas_volume": {"title": "Volume", "placeholder": "Enter volume"}, "climate_impact_energy_consumption_non_renewable_sources_gas_metrics": {"title": "Indicate metrics", "placeholder": "metric"}}, "climate_impact_energy_consumption_non_renewable_sources_coal": {"title": "Coal", "description": "Reports on the usage of coal as a non-renewable energy source, including consumption volume.", "climate_impact_energy_consumption_non_renewable_sources_coal_enabled": {"title": "Coal"}, "climate_impact_energy_consumption_non_renewable_sources_coal_volume": {"title": "Volume", "placeholder": "Enter volume"}, "climate_impact_energy_consumption_non_renewable_sources_coal_metrics": {"title": "Indicate metrics", "placeholder": "metric"}}, "climate_impact_energy_consumption_non_renewable_sources_diesel": {"title": "Diesel", "description": "Details the consumption of diesel as a non-renewable energy source, measured in volume.", "climate_impact_energy_consumption_non_renewable_sources_diesel_enabled": {"title": "Diesel"}, "climate_impact_energy_consumption_non_renewable_sources_diesel_volume": {"title": "Volume", "placeholder": "Enter volume"}, "climate_impact_energy_consumption_non_renewable_sources_diesel_metrics": {"title": "Indicate metrics", "placeholder": "metric"}}, "climate_impact_energy_consumption_non_renewable_sources_electricity": {"title": "Electricity", "description": "Indicates whether electricity is consumed as a non-renewable energy source, with volume metrics provided.", "climate_impact_energy_consumption_non_renewable_sources_electricity_enabled": {"title": "Electricity"}, "climate_impact_energy_consumption_non_renewable_sources_electricity_volume": {"title": "Volume", "placeholder": "Enter volume"}, "climate_impact_energy_consumption_non_renewable_sources_electricity_metrics": {"title": "Indicate metrics", "placeholder": "metric"}}, "climate_impact_energy_consumption_non_renewable_sources_other_sources": {"items": {"climate_impact_energy_consumption_non_renewable_sources_other_sources_enabled": {"title": "Energy source", "placeholder": "Enter energy source"}, "climate_impact_energy_consumption_non_renewable_sources_other_sources_volume": {"title": "Volume", "placeholder": "Enter volume"}, "climate_impact_energy_consumption_non_renewable_sources_other_sources_metrics": {"title": "Indicate metrics", "placeholder": "metric"}, "climate_impact_energy_consumption_non_renewable_sources_other_sources_type": {"type": "string", "title": "Energy source", "placeholder": "Enter energy source"}}}}, "climate_impact_energy_consumption_renewable_sources": {"title": "Check all types of RENEWABLE sources of energy consumed by the Company for the last full calendar year", "description": "Requests details on renewable energy consumption for the last full calendar year, including volume and metric for each type.", "addItemLabel": "Add Other Source", "climate_impact_energy_consumption_renewable_sources_district_heating": {"title": "District heating", "description": "Specifies consumption details of renewable district heating, measured in volume.", "climate_impact_energy_consumption_renewable_sources_district_heating_enabled": {"title": "District heating"}, "climate_impact_energy_consumption_renewable_sources_district_heating_volume": {"title": "Volume", "placeholder": "Enter volume"}, "climate_impact_energy_consumption_renewable_sources_district_heating_metrics": {"title": "Indicate metrics", "placeholder": "metric"}}, "climate_impact_energy_consumption_renewable_sources_biogas": {"title": "Biogas", "description": "Indicates whether biogas is consumed as a renewable energy source, along with volume metrics.", "climate_impact_energy_consumption_renewable_sources_biogas_enabled": {"title": "Biogas"}, "climate_impact_energy_consumption_renewable_sources_biogas_volume": {"title": "Volume", "placeholder": "Enter volume"}, "climate_impact_energy_consumption_renewable_sources_biogas_metrics": {"title": "Indicate metrics", "placeholder": "metric"}}, "climate_impact_energy_consumption_renewable_sources_electricity": {"title": "Electricity", "description": "Reports on the consumption of renewable electricity, including volume details.", "climate_impact_energy_consumption_renewable_sources_electricity_enabled": {"title": "Electricity"}, "climate_impact_energy_consumption_renewable_sources_electricity_volume": {"title": "Volume", "placeholder": "Enter volume"}, "climate_impact_energy_consumption_renewable_sources_electricity_metrics": {"title": "Indicate metrics", "placeholder": "metric"}}, "climate_impact_energy_consumption_renewable_sources_biomass": {"title": "Biomass", "description": "Details the consumption of biomass as a renewable energy source, with corresponding volume metrics.", "climate_impact_energy_consumption_renewable_sources_biomass_enabled": {"title": "Biomass"}, "climate_impact_energy_consumption_renewable_sources_biomass_volume": {"title": "Volume", "placeholder": "Enter volume"}, "climate_impact_energy_consumption_renewable_sources_biomass_metrics": {"title": "Indicate metrics", "placeholder": "metric"}}, "climate_impact_energy_consumption_renewable_sources_other_sources": {"items": {"climate_impact_energy_consumption_renewable_sources_other_sources_enabled": {"title": "Energy source", "placeholder": "Enter energy source"}, "climate_impact_energy_consumption_renewable_sources_other_sources_volume": {"title": "Volume", "placeholder": "Enter volume"}, "climate_impact_energy_consumption_renewable_sources_other_sources_metrics": {"title": "Indicate metrics", "placeholder": "metric"}, "climate_impact_energy_consumption_renewable_sources_other_sources_type": {"type": "string", "title": "Energy source", "placeholder": "Enter energy source"}}}}, "climate_impact_energy_consumption_efficiency_plan": {"title": "Does the Company have an energy efficiency improvement plan?", "description": "Indicates whether the company has implemented an energy efficiency improvement plan.", "placeholder": "Answer"}, "climate_impact_energy_consumption_efficiency_plan_details": {"title": "Please provide details", "description": "Requests additional details regarding the energy efficiency improvement plan, if one is in place.", "placeholder": "Enter efficiency plan details"}}}, "green_transition": {"title": "Green transition readiness assessment", "description": "To overcome climate change challenges European Union is taking action by increasing regulatory requirements, aiming to help supporting those activities and actions, which positively contribute to limiting negative effects of climate change. For large companies it means increase of regulatory requirements in the forthcoming years according to Corporate Sustainability Reporting Directive regulation (eur-lex.europa.eu/legal-content/EN/TXT/?uri=CELEX%3A32022L2464). At the same time increased awareness towards climate change and threats related to these processes, may lead to change in market behaviour, e.g. consumers giving preference to more sustainable goods and services, business partners willing to cooperate with companies, which practice sustainable approaches, investors - opting to invest in more sustainable businesses. Answers to these questions will help to understand your Company's readiness to green transition.", "green_transition_sustainability_strategy": {"title": "Sustainability Strategy", "green_transition_sustainability_strategy_has_strategy": {"title": "Does the Company have a sustainability strategy in place or does it exist as an integral part of other documents or policies?", "description": "Determines if the company has a formal sustainability strategy or if sustainability is integrated within other policies or documents.", "placeholder": "Answer"}, "green_transition_sustainability_strategy_strategy_document": {"title": "Link to sustainability strategy", "description": "Requests a link to or submission of the document detailing the company’s sustainability strategy.", "placeholder": "Please add link"}, "green_transition_sustainability_strategy_additional_documents": {"title": "Upload attachment", "description": "Requests a link to or submission of the document detailing the company’s sustainability strategy."}}, "green_transition_sustainability_report": {"title": "Does the Company prepare sustainability report or has it as an integral part of any other reports?", "description": "Indicates whether the company produces a dedicated sustainability report or integrates sustainability disclosures within other reports.", "placeholder": "Answer"}, "green_transition_other_sustainability_reports": {"title": "Other Sustainability Reports", "green_transition_other_sustainability_reports_has_reports": {"title": "Does the Company prepare other reports related to sustainability (e.g., environmental reports) or does it use other forms of communication for its sustainability plans?", "description": "Asks if the company prepares additional sustainability-related reports (e.g., environmental reports) or uses alternative communication channels for its sustainability plans.", "placeholder": "Answer"}, "green_transition_other_sustainability_reports_report_document": {"title": "Link to other sustainability reports", "description": "Requests a link to or submission of any additional sustainability-related report documents.", "placeholder": "Please add link"}, "green_transition_other_sustainability_reports_additional_documents": {"title": "Upload attachment", "description": "Requests a link to or submission of any additional sustainability-related report documents."}}, "green_transition_sustainability_certification": {"title": "Sustainability Certification", "green_transition_sustainability_certification_has_certification": {"title": "Does the Company hold certification of Sustainability standards or Environmental Product Declaration (EPD)?", "description": "Determines if the company holds certifications for sustainability standards or an Environmental Product Declaration (EPD).", "placeholder": "Answer"}, "green_transition_sustainability_certification_certification_details": {"title": "Please provide details", "description": "Provides space for the company to detail its sustainability certification or EPD information if applicable.", "placeholder": "Please provide details"}}, "green_transition_environmentally_friendly_goods": {"title": "Environmentally Friendly Goods", "green_transition_environmentally_friendly_goods_has_eco_goods": {"title": "Does the Company supply environmentally friendly goods or use environmentally friendly approaches?", "description": "Evaluates whether the company supplies environmentally friendly goods or employs circular economic approaches.", "placeholder": "Answer"}, "green_transition_environmentally_friendly_goods_eco_goods_details": {"title": "Please provide details", "description": "Requests further details on the company’s environmentally friendly practices or product lines.", "placeholder": "Please provide details"}}, "green_transition_epd_goods_services": {"title": "Environmental Product Declaration (EPD)", "green_transition_epd_goods_services_has_epd": {"title": "Does the Company hold EPD on produced goods/services?", "description": "Indicates whether the company holds an Environmental Product Declaration (EPD) for its produced goods or services.", "placeholder": "Answer"}, "green_transition_epd_goods_services_epd_details": {"title": "Please provide details", "description": "Requests additional details regarding the EPD if one is held by the company.", "placeholder": "Please provide details"}}, "green_transition_carbon_offsetting": {"title": "Carbon Offsetting", "green_transition_carbon_offsetting_has_offsetting": {"title": "Does the Company have carbon offsetting in place?", "description": "Determines whether the company participates in carbon offsetting initiatives to mitigate its carbon footprint.", "placeholder": "Answer"}, "green_transition_carbon_offsetting_offsetting_details": {"title": "Please provide details", "description": "Provides space for the company to detail its carbon offsetting practices, if applicable.", "placeholder": "Please provide details"}}, "green_transition_international_standards": {"title": "International Standards", "green_transition_international_standards_follows_standards": {"title": "Does the Company follow any international or local best practice standards?", "description": "Assesses whether the company adheres to international or local best practice standards or has committed to external sustainability commitments.", "placeholder": "Answer"}, "green_transition_international_standards_standards_details": {"title": "Please provide details", "description": "Requests additional details on the best practice standards or external commitments the company has signed.", "placeholder": "Please provide details"}}, "green_transition_eu_taxonomy": {"title": "Has the Company implemented any activities contributing to the following EU Taxonomy of environmental objectives?", "description": "Determines if the company has implemented activities that contribute to the EU Taxonomy environmental objectives, and requests detailed information if so.", "green_transition_eu_taxonomy_climate_change_mitigation": {"green_transition_eu_taxonomy_climate_change_mitigation_implemented": {"title": "Climate change mitigation", "description": "Indicates if the company has initiatives aimed at mitigating climate change in line with EU Taxonomy objectives.", "placeholder": "Answer"}, "green_transition_eu_taxonomy_climate_change_mitigation_details": {"title": "Please provide details", "description": "Requests detailed information on the specific activities contributing to climate change mitigation implemented by the company.", "placeholder": "Please provide details"}}, "green_transition_eu_taxonomy_climate_change_adaptation": {"green_transition_eu_taxonomy_climate_change_adaptation_implemented": {"title": "Climate change adaptation", "description": "Evaluates whether the company has measures in place to adapt to the impacts of climate change, as per EU Taxonomy requirements.", "placeholder": "Answer"}, "green_transition_eu_taxonomy_climate_change_adaptation_details": {"title": "Please provide details", "description": "Requests detailed information on the specific activities contributing to climate change adaptation implemented by the company.", "placeholder": "Please provide details"}}, "green_transition_eu_taxonomy_sustainable_water_use": {"green_transition_eu_taxonomy_sustainable_water_use_implemented": {"title": "The sustainable use and protection of water and marine resources", "description": "Assesses the company’s activities related to the sustainable use and protection of water and marine resources.", "placeholder": "Answer"}, "green_transition_eu_taxonomy_sustainable_water_use_details": {"title": "Please provide details", "description": "Requests detailed information on the specific activities contributing to the sustainable use and protection of water and marine resources implemented by the company.", "placeholder": "Please provide details"}}, "green_transition_eu_taxonomy_circular_economy": {"green_transition_eu_taxonomy_circular_economy_implemented": {"title": "The transition to a circular economy", "description": "Evaluates if the company is transitioning to a circular economy by implementing relevant activities.", "placeholder": "Answer"}, "green_transition_eu_taxonomy_circular_economy_details": {"title": "Please provide details", "description": "Requests detailed information on the specific activities contributing to the transition to a circular economy implemented by the company.", "placeholder": "Please provide details"}}, "green_transition_eu_taxonomy_pollution_prevention": {"green_transition_eu_taxonomy_pollution_prevention_implemented": {"title": "Pollution prevention and control", "description": "Assesses if the company has measures in place for pollution prevention and control in accordance with environmental standards.", "placeholder": "Answer"}, "green_transition_eu_taxonomy_pollution_prevention_details": {"title": "Please provide details", "description": "Requests detailed information on the specific activities contributing to pollution prevention and control implemented by the company.", "placeholder": "Please provide details"}}, "green_transition_eu_taxonomy_biodiversity_protection": {"green_transition_eu_taxonomy_biodiversity_protection_implemented": {"title": "The protection and restoration of biodiversity and ecosystems", "description": "Indicates if the company has initiatives focused on protecting and restoring biodiversity and ecosystems.", "placeholder": "Answer"}, "green_transition_eu_taxonomy_biodiversity_protection_details": {"title": "Please provide details", "description": "Requests detailed information on the specific activities contributing to biodiversity protection implemented by the company.", "placeholder": "Please provide details"}}}}, "nature": {"title": "Nature", "description": "Biodiversity is a variety of forms of life on earth - such as different species of plants and animals found within specific regions. Biodiversity is essential for functioning of all processes: from ensuring survival of all life forms to climate regulation and economic stability. Biodiversity is also a fundamental component of long-term business survival. Biodiversity underpins the benefits that businesses derive from natural capital and supports the key ecosystem functions to maintain quality of soil, water and air. While the private sector can have a negative effect on Biodiversity, it can also be part of the solution. The resources and impact of the private sector offer important opportunities for innovative and effective contributions to conservation. Answers provided to the questions of this section will allow to get basic information on the impact, which business activity of your Company can have on biodiversity.", "nature_biodiversity_sensitive_areas": {"title": "Does the Company operate in or near biodiversity-sensitive areas (including the Natura 2000 network of protected areas, UNESCO Natural World Heritage sites and Key Biodiversity Areas)?", "description": "Determines if the company operates in or near biodiversity-sensitive areas, such as Natura 2000 sites or UNESCO World Heritage areas.", "placeholder": "Answer"}, "nature_negative_impact_on_nature": {"title": "Can your Company's operations negatively affect nature's realms in any way: the oceans, freshwater, land, atmosphere?", "description": "Assesses whether the company’s operations negatively impact natural realms such as oceans, freshwater, land, or the atmosphere.", "placeholder": "Answer"}, "nature_negative_impact_comments": {"title": "If yes, please comment:", "description": "Provides an opportunity for the company to comment on any negative impacts its operations may have on nature.", "placeholder": "Please provide details"}, "nature_plan_to_reduce_impact": {"title": "If you answered Yes to question 1. or 2., have you developed a plan of measures to prevent or reduce possible negative impacts?", "description": "Determines if the company has developed a plan to prevent or reduce negative environmental impacts identified in previous questions.", "placeholder": "Answer"}, "nature_measures_details": {"title": "Please indicate the measure(-s) and comment(-s) for selected measures", "description": "Requests detailed information on the specific measures and comments for each mitigation action undertaken to reduce negative environmental impacts.", "helpText": "Requests detailed information on the specific measures and comments for each mitigation action undertaken to reduce negative environmental impacts.", "nature_measures_details_avoid": {"title": "Avoid: Actions taken to avoid causing a negative impact.", "description": "Describes actions taken by the company to avoid causing any negative environmental impacts.", "placeholder": "Please provide details"}, "nature_measures_details_mitigate": {"title": "Mitigate: measures taken to reduce the significance/intensity of any negative impacts that cannot be completely avoided.", "description": "Describes measures implemented to reduce the significance or intensity of unavoidable negative environmental impacts.", "placeholder": "Please provide details"}, "nature_measures_details_restore": {"title": "Restore: Actions taken to restore ecosystems degraded from negative impacts that cannot be fully avoided and/or minimized.", "description": "Outlines actions taken by the company to restore ecosystems that have been degraded due to unavoidable negative impacts.", "placeholder": "Please provide details"}, "nature_measures_details_compensate": {"title": "Compensate: Actions taken to compensate for residual adverse effects that cannot be prevented, reduced and/or restored.", "description": "Describes compensation measures taken to address any residual adverse environmental effects that cannot be fully mitigated.", "placeholder": "Please provide details"}}, "nature_natural_capital_dependency": {"title": "Is your Company's activity critically dependant on natural capital: renewable and non-renewable natural resources such as plants, forest, animals, air, water, soil and minerals.", "description": "Assesses the extent to which the company’s activities depend on natural capital, including both renewable and non-renewable resources.", "placeholder": "Answer"}, "nature_natural_capital_plan": {"title": "If Yes, have you developed a plan of measures to mitigate or reduce the dependency on natural capital?", "description": "Determines if the company has developed measures to mitigate or reduce its dependency on natural capital.", "placeholder": "Answer"}, "nature_natural_capital_measures": {"title": "If yes, please indicate the measures", "description": "Requests details on specific measures implemented to reduce dependency on natural capital.", "placeholder": "Please provide details"}, "nature_waste_management": {"title": "Does the Company have a waste management procedure in place?", "description": "Indicates whether the company has a formal waste management procedure in place to manage and reduce waste.", "placeholder": "Answer"}, "nature_nature_commitments": {"title": "Has your Company committed to Nature positive objectives, the Taskforce on Nature-related Financial Disclosures(TNFD) framework, Science-based Targets for Nature or similar initiatives?", "description": "Determines if the company has committed to nature-positive objectives or frameworks such as the TNFD.", "placeholder": "Answer"}, "nature_nature_commitments_details": {"title": "If yes, please provide details:", "description": "Requests additional details on the company’s commitments to nature-positive initiatives or frameworks.", "placeholder": "Please provide details"}}, "social_governance": {"title": "Social and governance practices", "description": "Social and governance framework is essential in order to implement and maintain sustainability practices. Social area covers impact on surrounding community and workers (such as health and safety, diversity and inclusion). Corporate governance refers to processes, which ensure effective management of the Company (e.g. ethical conduct, risk management, transparency).", "social_governance_workforce_policies": {"title": "What policies and procedures related to benefits, engagement and human rights protection of own workforce the Company has in place?", "description": "Requests disclosure of the policies and procedures related to employee benefits, engagement, and human rights protection.", "placeholder": "Select Workforce Policies"}, "social_governance_end_user_policies": {"title": "What policies and procedures related to end-users the Company has in place?", "description": "Requests details on the policies and procedures in place to ensure product safety and social responsibility for end-users.", "placeholder": "Please provide details"}, "social_governance_safety_incidents": {"title": "Safety Incidents", "social_governance_safety_incidents_has_incidents": {"title": "Have there been any significant incidents related to the safety or quality of the Company's products or services in the last 3 years?", "description": "Inquires about any significant incidents related to the safety or quality of the company’s products or services over the last three years.", "placeholder": "Answer"}, "social_governance_safety_incidents_details": {"title": "If yes, please provide details", "description": "Provides space for the company to detail any incidents affecting product or service safety and quality.", "placeholder": "Please provide details"}}, "social_governance_stakeholder_engagement": {"title": "Stakeholder Engagement", "social_governance_stakeholder_engagement_has_programs": {"title": "Does the Company implement stakeholder engagement, impact assessment, and/or development programs to avoid, minimize or mitigate impacts of operation?", "description": "Assesses whether the company engages in stakeholder engagement, impact assessment, and development programs to mitigate operational impacts.", "placeholder": "Answer"}, "social_governance_stakeholder_engagement_details": {"title": "If yes, please provide details", "description": "Requests additional details on stakeholder engagement or impact assessment programs implemented by the company.", "placeholder": "Please provide details"}}, "social_governance_community_benefits": {"title": "Community Benefits", "social_governance_community_benefits_has_initiatives": {"title": "Does the Company have any initiatives to provide benefits to local communities or to underserved markets?", "description": "Determines if the company has initiatives aimed at providing benefits to local communities or underserved markets.", "placeholder": "Answer"}, "social_governance_community_benefits_details": {"title": "If yes, please provide details", "description": "Provides space for the company to describe initiatives targeted at supporting local communities.", "placeholder": "Please provide details"}}, "social_governance_supply_chain_esg": {"title": "ESG Criteria in Supply Chain", "description": "(e.g. working conditions, human rights, privacy, labour rights, health and safety protection, social and labour protection, environmental protection)", "social_governance_supply_chain_esg_has_policies": {"title": "Does the Company have policies and/or processes in place that cover and/or address ESG criteria within its supply chain?", "description": "Assesses if the company has established policies or processes to address ESG criteria within its supply chain.", "placeholder": "Answer"}, "social_governance_supply_chain_esg_details": {"title": "If yes, please provide details", "description": "Requests additional details on how the company ensures ESG compliance within its supply chain.", "placeholder": "Please provide details"}}, "social_governance_international_compliance": {"title": "International Compliance", "description": "including the principles and rights set out in the eight core conventions and the International Labour Organization Declaration on Fundamental Principles and Rights at Work and the International Bill of Human Rights (as well as local regulations regarding human and employee rights)?", "social_governance_international_compliance_complies": {"title": "Do the company's policies and procedures comply with the OECD Guidelines for Multinational Enterprises and the UN Guiding Principles on Business and Human Rights?", "description": "Determines if the company’s policies comply with international guidelines such as the OECD Guidelines and UN Guiding Principles on Business and Human Rights.", "placeholder": "Answer"}}, "social_governance_governance_policies": {"title": "Please indicate which areas are covered in the Company's internal policies and procedures:", "description": "Determines if the company’s policies comply with international guidelines such as the OECD Guidelines and UN Guiding Principles on Business and Human Rights.", "placeholder": "Select Governance Policies"}, "social_governance_materiality_assessment": {"title": "Has the Company performed a materiality assessment in the area ESG and identified material ESG related risks?", "description": "Determines if the company has performed an ESG materiality assessment to identify key environmental, social, and governance risks.", "placeholder": "Answer"}, "social_governance_supplier_code_of_conduct": {"title": "Has the Company developed a supplier code of conduct that specifies environmental, social, and governance requirements that suppliers must adhere to?", "description": "Assesses whether the company has developed a supplier code of conduct outlining the ESG standards required of its suppliers.", "placeholder": "Answer"}, "social_governance_supplier_monitoring": {"title": "To what extent does the Company monitor and assess the performance of suppliers in the field of sustainability?", "description": "Requests details on the extent to which the company monitors and assesses supplier sustainability performance.", "placeholder": "Answer"}, "social_governance_litigation": {"title": "Litigation and Controversies", "description": "e.g. water, waste, energy, biodiversity, environment regulations or water, energy, waste, biodiversity, environment disturbances, human and labor rights, health and safety, product/service of the company?", "social_governance_litigation_has_litigation": {"title": "Has the company been involved in any litigation, controversy or concerns in relation to non-compliance?", "description": "Inquires if the company has been involved in any litigation, controversy, or concerns related to non-compliance with environmental, social, or regulatory standards.", "placeholder": "Answer"}}, "social_governance_non_compliances": {"title": "Have there been non-compliances or controversies including a warning, fine, penalty, closure of the company?", "description": "Specifies whether the company has faced non-compliances, warnings, fines, or penalties due to regulatory breaches.", "placeholder": "Answer"}, "social_governance_privacy_breaches": {"title": "Have there been any records of substantiated complaints concerning breaches of customer privacy, incidents related to data protection and losses of customer data?", "description": "Inquires if there have been substantiated complaints regarding breaches of customer privacy or data protection issues.", "placeholder": "Answer"}, "social_governance_compliance_officer": {"title": "Does the Company have a designated officer responsible for ensuring Compliance with the company's internal governance policies and procedures?", "description": "Indicates whether the company has designated an officer responsible for ensuring compliance with internal governance policies.", "placeholder": "Answer"}}, "nfrd_reporting": {"title": "Please indicate the proportion of Taxonomy-eligible economic activities in the net turnover, capital expenditure (Capex) and operational expenditure (Opex):", "description": "Requests disclosure of the proportion of economic activities that are eligible under the EU Taxonomy framework.", "nfrd_reporting_taxonomy_eligible_activities": {"title": "Please indicate the proportion of Taxonomy-eligible economic activities in the net turnover, capital expenditure (Capex)", "description": "Requests disclosure of the proportion of economic activities that are eligible under the EU Taxonomy framework.", "helpText": "Requests disclosure of the proportion of economic activities that are eligible under the EU Taxonomy framework.", "nfrd_reporting_taxonomy_eligible_activities_sustainable_net_turnover": {"title": "Net turnover of environmentally sustainable activities (Taxonomy aligned)", "description": "Reports the net turnover from environmentally sustainable activities that are aligned with the EU Taxonomy.", "placeholder": "Enter percentage"}, "nfrd_reporting_taxonomy_eligible_activities_eligible_net_turnover": {"title": "Net turnover of Taxonomy eligible, but not environmentally sustainable activities (not Taxonomy aligned)", "description": "Reports the net turnover from activities that are eligible under the EU Taxonomy but are not considered environmentally sustainable.", "placeholder": "Enter percentage"}, "nfrd_reporting_taxonomy_eligible_activities_sustainable_capex": {"title": "Capex of environmentally sustainable activities (Taxonomy aligned)", "description": "Reports the capital expenditure on environmentally sustainable activities that are aligned with the EU Taxonomy.", "placeholder": "Enter percentage"}, "nfrd_reporting_taxonomy_eligible_activities_eligible_capex": {"title": "Capex of Taxonomy eligible, but unsustainable activities (not Taxonomy aligned)", "description": "Reports the capital expenditure on activities that are eligible under the EU Taxonomy but are not considered environmentally sustainable.", "placeholder": "Enter percentage"}, "nfrd_reporting_taxonomy_eligible_activities_sustainable_opex": {"title": "Opex of environmentally sustainable activities (Taxonomy aligned)", "description": "Reports the operational expenditure on environmentally sustainable activities that are aligned with the EU Taxonomy.", "placeholder": "Enter percentage"}, "nfrd_reporting_taxonomy_eligible_activities_eligible_opex": {"title": "Opex of Taxonomy eligible, but unsustainable activities (not Taxonomy aligned)", "description": "Reports the operational expenditure on activities that are eligible under the EU Taxonomy but are not considered environmentally sustainable.", "placeholder": "Enter percentage"}}}}, "selfAssessmentForm": {"title": "UNIFIED CLIENT QUESTIONNAIRE ON ENVIRONMENTAL AND CLIMATE-RELATED DISCLOSURES", "introduction": {"title": "Introduction", "label": "Introduction", "main_text": {"title": "", "description": "This ESG self-assessment is based on the Voluntary Sustainability Reporting Standard for Small and Medium-sized Enterprises (VSME). We’ve pre-filled parts of the assessment using publicly available information, but you are welcome to review and update the content as needed. Once completed, your results will be available under the My Company screen, with a detailed overview in the ESG Assessment tab. You will gain access to personalised recommendations and examples of industry best practices. Your results will also be shared with SEB, who is co-sponsoring this initiative.\nIn case you are at the start of your ESG journey and have questions about the self assesment or ESG in general, we are delighted to offer a 1 hour session with an ESG expert. Please click \"request consultancy\" link on top right corner of the page and we will be in contact."}}, "general": {"title": "General", "label": "General", "description": "Please specify for which financial year you would be conducting this assessment:", "survey_fin_year": {"title": "Financial year", "placeholder": "Enter Financial year"}, "survey_revenue_for_year": {"title": "Total Revenue (€)", "placeholder": "Enter Total Revenue"}}, "esg_management_strategy": {"title": "ESG Management & Strategy", "practices_for_transitioning_towards_a_more_sustainable_economy": {"title": "Sustainability Transition Practices", "description": "Please specify if any of the following is relevant for your company and provide details where possible:", "if_sust_vision": {"title": "We have sustainability vision", "placeholder": "Answer", "description": "A sustainability vision typically refers to a company's strategic goals and commitments related to Environmental, Social, and Governance (ESG) aspects. It serves as a guiding statement that aligns the company's activities with sustainable practices, aiming to increase positive impacts on the environment and society while ensuring robust governance practices.\n\nThe vision should be clearly articulated in internal company documentation, such as a sustainability report, strategy document, or company mission and values statement. It might be found in sections that outline the business model, strategy, or specific ESG policies. In your company’s documents, look for areas where strategic objectives or commitments to sustainability and responsible governance are discussed."}, "sust_vision": {"title": "Sustainability vision description", "placeholder": "Please describe briefly your sustainability vision"}, "if_sust_strategy": {"title": "We have sustainability strategy", "placeholder": "Answer", "description": "An ESG strategy refers to a company's approach to environmental, social, and governance issues. This involves integrating sustainability into the business model, operations, and overall strategy to address environmental risks, improve social equity, and ensure strong governance structures. \n\nIdeally, your company's ESG strategy document may include details about the business model, sustainability-related initiatives, key markets, business relationships, and any practices, policies, or future initiatives for transitioning towards a sustainable economy. \n\nSuch a document can often be found as part of your company's internal sustainability report or strategic planning documents. Look for sections detailing practices, policies, and future sustainability initiatives, as these will provide insight into the business's strategic approach to ESG."}, "sust_strategy": {"title": "Sustainability strategy description", "placeholder": "Please describe briefly your sustainability strategy"}, "sust_strategy_ref": {"title": "Sustainability strategy reference", "sust_strategy_ref_link": {"placeholder": "Please add link", "title": "Please provide webpage reference"}, "sust_strategy_ref_additional_documents": {"title": "Please upload a document"}}, "if_sustainable_practices": {"title": "We follow sustainable practices", "placeholder": "Answer", "description": "Sustainable practices refer to specific operational behaviors or measures that a company undertakes to reduce its negative impacts and enhance its positive contributions to people and the environment. \n\nExamples may include:\nReducing energy and water consumption.\nMinimizing greenhouse gas (GHG) emissions and pollution.\nPromoting equal treatment and improving working conditions.\nEngaging in sustainability-related training for employees.\nAdopting circular economy principles like reducing waste or improving resource efficiency.\n\nThese practices may not be necessarily formalized in policy but are concrete actions taken by the company in its day-to-day operations to support environmental and social sustainability goals​."}, "sustainable_practices": {"title": "Sustainable practices description", "placeholder": "Please specify briefly the practices you follow"}, "if_sust_commitments": {"title": "We have external sustainability commitments", "placeholder": "Answer", "description": "External sustainability commitments are formal pledges or obligations that a company takes on voluntarily or due to external requirements, usually aligning with recognized frameworks, sector initiatives, or supply chain demands. \n\nThese could include:\nMembership in voluntary sustainability initiatives or partnerships.\nAdherence to sustainability-related certifications or standards (e.g., ISO 14001).\nCommitments made in response to customer or investor ESG data requests.\nAlignment with climate transition goals such as the UN SDGs or the Paris Agreement."}, "sust_commitments": {"title": "Sustainability commitments description", "placeholder": "Please specify any international or local standards or commitments"}, "if_sust_certification": {"title": "We hold sustainability certifications", "placeholder": "Answer", "description": "Sustainability certifications refer to accolades or qualifications that an organization obtains to demonstrate its commitment to sustainable practices. These certifications often confirm that a company adheres to specific environmental, social, and economic standards. Examples include ISO 14001 for environmental management systems and the EU Ecolabel for products. Obtaining and maintaining such certifications help organizations showcase their commitment to sustainability and can improve reputational trust and market opportunities.\n\nIn your company, documentation about sustainability certifications might be found in sustainability reports, environmental management system documents, or any other official publications where the company highlights its achievements in meeting sustainability standards."}, "sust_certification": {"title": "Sustainability certifications descriptions", "placeholder": "Please list the certifications you hold"}, "esg_initiatives": {"title": "Sustainability initiatives", "placeholder": "Please specify", "description": "Sustainability initiatives refer to the specific actions, policies, or plans a company puts in place to transition towards a more sustainable economy. These can include efforts to reduce water and electricity consumption, decrease greenhouse gas emissions, prevent pollution, improve product safety, enhance working conditions, promote equal treatment, and provide sustainability training for the workforce. They may also involve partnerships related to sustainability projects.\n\nExamples of sustainability initiatives could be:\n\nImplementing energy-efficient processes or technology.\n\nAdopting renewable energy sources for operations.\n\nLaunching recycling or waste reduction programs.\n\nProviding sustainability training for employees.\n\nEstablishing partnerships for community-based sustainability projects.\n\nIn your company, these initiatives might be covered by various departments or roles responsible for environmental management, compliance, human resources (for training), or corporate social responsibility."}}, "human_rights_policies": {"title": "Human Rights", "description": "Does your company have any of the following human rights topics covered in policies or procedures?", "helpText": "Human rights generally refer to the basic rights and freedoms that belong to every person from birth until death. They apply regardless of where you are from, what you believe, or how you choose to live your life. These rights are universal and are often expressed and guaranteed by law in the forms of treaties, customary international law, general principles, and other sources of international law.\n\nIn Baltics, like many other countries, human rights considerations can pose risks to businesses if not adequately addressed. For smaller companies, the minimum expectation would typically include having a code of conduct or human rights policy covering aspects such as child labour, forced labour, human trafficking, discrimination, and ensuring accident prevention measures are in place.\n\nCompanies are expected to be aware of and report any incidents related to severe negative human rights issues within their workforce and supply chain, such as cases. Additionally, it is beneficial for companies to have a mechanism for handling complaints related to human rights issues within their operations.\n\nOverall, businesses should aim to align their practices with internationally recognized human rights standards and local regulations to mitigate risks and promote ethical conduct.", "if_child_labour_policy": {"title": "Child labour", "placeholder": "Answer", "description": "Child labour refers to work that deprives children of their childhood, potential, and dignity, which harms their physical and mental development. It includes work that is mentally, physically, socially, or morally dangerous and/or interferes with their schooling by preventing them from attending school.\n\nExamples of child labour can include children working in hazardous environments, performing work that requires heavy lifting, or any job that exposes them to health risks. It might also include children working long hours that keep them from going to school.\n\nIn your company, child labour issues might be covered in sections regarding human rights policies or codes of conduct. "}, "if_forced_labour_policy": {"title": "Forced labour", "placeholder": "Answer", "description": "Forced labour is defined as all work or service exacted from any person under the threat of penalty and for which the person has not offered themselves voluntarily, according to the International Labour Organization (ILO) Forced Labour Convention, 1930 (No.29). This term includes any situation where individuals are coerced by any means to perform work.\n\nExamples of forced labour might include bonded labour, where workers are forced to work to repay a debt; scenarios where individuals are coerced or deceived into work.\n\nWithin a company, forced labour issues might be found in various areas such as the supply chain, where suppliers might engage in unethical labour practices. It may also be present in areas where high-pressure sales targets push employees into unreasonable hours without proper compensation. "}, "if_human_trafficking": {"title": "Human trafficking", "placeholder": "Answer", "description": "Human trafficking involves the recruitment, transportation, transfer, harboring, or reception of persons by means such as force, coercion, abduction, fraud, or deception for the purpose of exploitation.\n\nExamples of human trafficking include situations where individuals are forced into labor under threat, deceit, or coercion, or when individuals are exploited for commercial sex or forced to work in conditions akin to slavery.\n\nIn your company, issues related to human trafficking may be covered in various areas such as the supplier codes or under human rights policies and processes if you have such policies in place. "}, "if_discrimination_policy": {"title": "Discrimination", "placeholder": "Answer", "description": "Discrimination refers to treating someone unfavorably because of their characteristics such as race, gender, age, or ethnicity. It can occur directly when one person is treated less favorably than another in a similar situation. Indirect discrimination happens when a seemingly neutral rule disadvantages a certain group sharing similar characteristics.\n\nExamples of discrimination may include a company having hiring practices that favor certain demographics or setting requirements that inadvertently exclude others, such as specific physical conditions that aren't necessary for job performance but favor one group over another.\n\nIn your company, discrimination can be addressed through employee handbook, human rights policies,  a code of conduct, and creating a fair complaint-handling mechanism to ensure such incidents are reported and acted upon. This may involve human resources policies and staff training programs to promote inclusivity and prevent discriminatory practices."}, "if_accident_prevention": {"title": "Accident prevention", "placeholder": "Answer", "description": "Accident prevention refers to policies and initiatives aimed at preventing workplace accidents and ensuring employee safety and wellbeing. This includes measures to reduce physical risks and fostering a safe, inclusive work environment free from discrimination and harassment.\n\nIn your company, accident prevention could be addressed through:\nSafety Policies: Implementing clear safety rules and guidelines to prevent accidents.\nTraining Programs: Conducting regular safety training for employees to handle equipment and respond to emergencies.\nSafety Equipment: Providing necessary protective gear and equipment.\nIncident Reporting Systems: Establishing mechanisms for employees to report potential hazards or accidents.\n\nThis might be covered under your company's health and safety programs or human rights policies."}, "other_hr_policy": {"title": "Other human rights", "placeholder": "Please specify", "description": "\"Other human rights\" generally refer to various human rights aspects not specifically categorized, such as privacy rights, freedom of speech, right to assembly, or any aspects specific to your industry not already listed under standard human rights categories. Examples could include:\n\nPrivacy Rights: Ensuring data protection and respecting employees' personal information.\nFreedom of Expression: Allowing employees to express their opinions freely.\nRight to Health: Ensuring safe and healthy working conditions beyond just accident prevention.\nCultural Rights: Respecting cultural diversity and inclusion within the workplace.\n\nIn a company, these could be integrated into policies covering data protection, workplace safety beyond statutory requirements, or policies promoting diversity and inclusion. They might also be part of comprehensive ethical guidelines or codes of conduct that align with international human rights principles."}}, "business_conduct_policies": {"title": "Business Conduct", "description": "Does your company have any of the following business conduct topics covered in policies or procedures?", "if_bribery_policy": {"title": "<PERSON><PERSON>bery", "placeholder": "Answer", "description": "Antibribery refers to practices aimed at preventing individuals or organizations from offering or accepting illegal incentives, such as money, gifts, or other advantages, to alter the behavior of the recipient in some way that constitutes a breach of trust or legality.\n\nExamples of antibribery measures can include:\nImplementing policies that prohibit any form of bribery and corruption.\nProviding training to employees on identifying and avoiding bribery situations.\nSetting up a whistleblower system to report bribery issues confidentially.\nRegular audits and risk assessments to detect and prevent bribery within business operations or supply chains.\n\nAntibribery measures may be covered within a company under its business ethics or corporate culture guidelines. This is often part of broader business conduct policies, which may also cover the protection of whistleblowers and the management of supplier relationships."}, "if_corruption_policy": {"title": "Corruption", "placeholder": "Answer", "description": "Corruption is defined as the abuse of entrusted power for personal gain. This can be carried out by individuals or organizations. It also covers the offering or acceptance of gifts, loans, rewards, or other advantages as an inducement for dishonest, illegal, or breach of trust actions within a business context.\n\nCorruption may be addressed within policies related to business ethics, anti-corruption, and anti-bribery. This would typically cover the reporting of any convictions and fines related to violations of anti-corruption and anti-bribery laws. Additionally, these aspects can be integrated into codes of conduct, corporate governance frameworks, and through training programs on ethical business practices."}, "if_fraud_policy": {"title": "<PERSON><PERSON>", "placeholder": "Answer", "description": "Fraud is a type of corruption that involves deceit or trickery for personal or financial gain. It can include activities like falsifying financial statements, embezzlement, or misrepresenting the business's financial status to gain loans or investment.\n\nExamples of fraud may include:\nFalsifying Financial Records: Manipulating accounting records to present a false financial position.\nEmbezzlement: Misappropriating funds that belong to an employer or company.\nInsurance Fraud: Making false claims to obtain benefits from insurance policies.\n\nIn a company, fraud may be covered under policies related to anti-corruption and financial controls. It would typically be addressed in the code of conduct, employee training programs, or through specific anti-fraud policies that outline how the company prevents and addresses fraudulent activities."}, "if_whistleb_policy": {"title": "Whistleblowing", "placeholder": "Answer", "description": "Whistleblowing is the act of reporting wrongdoing, unethical behavior, or corruption within an organization. Examples include reporting fraud, safety violations, harassment, or any illegal activities within the company.\n\nIn your company, whistleblowing might be covered under business ethics or corporate culture policies, which include the communication channels for reporting and protection of whistleblowers. These policies are part of broader business conduct matters, ensuring ethical behavior and compliance within your organization."}, "if_supplier_policy": {"title": "Supplier code of conduct", "placeholder": "Answer", "description": "A Supplier Policy or Supplier Code of Conduct is a set of guidelines and management principles that a company establishes to govern its interactions and transactions with suppliers. It is part of the broader business conduct of an organization and often includes the management of relationships with suppliers, focusing on practices such as fair payment terms and anti-corruption measures.\n\nExamples of matters regulated in supplier policies can include:\nFair Payment Practices: Ensuring that all payments to suppliers are timely and based on clearly defined terms to support smaller suppliers.\nEthical Sourcing: Requiring suppliers to adhere to ethical labor practices, such as prohibiting child labor or forced labor.\nEnvironmental Standards: Encouraging or requiring suppliers to engage in environmentally sustainable practices.\nAnti-corruption and Bribery: Implementing measures to prevent corrupt practices and bribery in transactions with suppliers.\n\nThese policies are typically managed by the department responsible for procurement or supply chain management within a company. They may be documented in the company’s corporate policies or the procurement section."}, "if_aml_policy": {"title": "Anti-Money Laundering", "placeholder": "Answer", "description": "AML stands for Anti-Money Laundering, which refers to a set of procedures, laws, and regulations designed to stop the practice of generating income through illegal actions. Money laundering involves transforming the profits of crime and corruption into ostensibly legitimate assets. \n\nExamples of AML activities within a company could include establishing a customer due diligence process, ongoing monitoring of customer transactions, and maintaining adequate records of transactions. Additionally, companies may implement training programs for employees to recognize and report suspicious activity.\n\nIn your company, AML could be covered under business conduct policies or compliance processes, which ensure adherence to legal, ethical, and professional standards."}}}, "climate_impact_energy_use": {"title": "Climate Impact & Energy Use", "energy_consumption": {"title": "Energy Consumption", "description": "Please specify your company's energy consumption.", "helpText": "Energy consumption refers to the amount of energy used by a business, including the type of energy, such as fossil fuels or renewable sources. It is essential to report energy consumption to understand and manage environmental impacts, especially those related to climate change.\n\nEnergy consumption includes total energy consumption broken down by fossil fuels and electricity, as well as the consumption of purchased or self-generated electricity from renewable sources.\n\nWhen calculating energy consumption, it's important to include energy used within the organization's boundaries but not energy that is produced and sold to third parties. Energy consumption should not be offset by energy production, even if it is generated on-site and sold.", "total_energy_cons": {"title": "Total Energy consumption (MWh)", "placeholder": "MWh", "description": "Total energy consumption refers to the amount of energy used by a company during its operations. It includes all types of energy consumed, such as electricity, fuels (like oil, gas, and coal), and any renewable energy sources utilized. This data helps in assessing the climate-related impacts of an organization and can be disaggregated into different types of energy sources, such as fossil fuels and renewable energy.\n\nIn your company, energy consumption could typically be tracked through utility bills for electricity and gas, fuel receipts for company vehicles, and records of any in-house energy generation or renewable energy usage."}, "renewable_electricity_cons": {"title": "Renewable electricity consumption (MWh)", "placeholder": "MWh", "description": "Renewable electricity consumption refers to the use of electricity generated from renewable non-fossil sources. These include wind, solar, geothermal, hydropower, biomass, landfill gas, sewage treatment plant gas, and biogas.\n\nIn your company, renewable electricity consumption can be featured in various ways:\nUtility Bills: Companies can purchase electricity from their utility providers that include a portion or all of renewable energy.\nOn-Site Generation: Utilization of solar panels, wind turbines, or other renewable sources installed on company premises to generate electricity.\nEnergy Certificates: Purchase of renewable energy certificates (RECs) that certify that a specific amount of energy consumed by the company is sourced from renewables.\n\nEnsure that when assessing this aspect, you consider any electricity that your company consumes, whether it is purchased from an external source or generated on-site with renewable resources."}, "nonrenewable_electricity_cons": {"title": "Non-Renewable electricity consumption (MWh)", "placeholder": "MWh", "description": "Non-renewable electricity consumption refers to the use of electricity generated from sources that cannot be replenished in a short period, such as fossil fuels—coal, oil, and natural gas. Examples of non-renewable electricity include electricity generated from coal-fired power plants, natural gas plants, and nuclear energy.\n\nNon-renewable electricity consumption might be tracked in utility billing records, where the electricity usage is broken down. "}, "renewable_fuels_cons": {"title": "Renewable fuels consumption (MWh)", "placeholder": "MWh", "description": "Renewable fuels consumption refers to the use of energy sources that are naturally replenished. These include biofuels such as biodiesel and bioethanol, which are produced from biomass like vegetable oils, animal fats, grain starch, oil seeds, or other organic materials. Additionally, biogas produced from decaying organic matter is considered a renewable fuel.\n\nYou may find this info through departments or teams responsible for energy management, facilities management, or environmental sustainability initiatives. They could track renewable energy usage in operations and report it as part of the company’s energy consumption data.\n\nSince companies may use different types of fuels with varying energy content, in order to ensure consistent data quality and comparability, the indicated metric is presented in kilowatt-hours (kWh). Generally, energy content data can be obtained from the producer or distributor. The approximate values are as follows:\nWood pellets ~4,700–5,200 kWh/ton\nBiodiesel ~10,000–12,000 kWh/ton\nBioethanol ~6,500–7,000 kWh/ton\nBiogas (60% CH₄) ~6–6.5 kWh/m³"}, "nonrenewable_fuels_cons": {"title": "Non-Renewable fuels consumption (MWh)", "placeholder": "MWh", "description": "Non-renewable fuel consumption refers to the use of energy sources that cannot be replenished in a short period of time. These include fossil fuels such as coal, oil, natural gas, petroleum, and others.\n\nExamples of non-renewable fuels your company might be using include:\nNatural gas used for heating or industrial processes.\nGasoline and diesel used in company vehicles or machinery.\nCoal used in energy generation or production processes.\n\nSince companies may use different types of fuels with varying energy content, in order to ensure consistent data quality and comparability, the indicated metric is presented in kilowatt-hours (kWh). Generally, energy content data can be obtained from the fuel producer or distributor. The approximate values are as follows:\n\nDiesel fuel ~11,700 kWh/ton\nGasoline ~12,000 kWh/ton\nNatural gas ~13,100 kWh/ton or ~11 kWh/m³"}}, "ghg_emissions": {"title": "GHG Emissions", "description": "Please specify your company's GHG emissions:", "helpText": "Greenhouse Gas (GHG) emissions refer to the release of gases that contribute to the greenhouse effect by absorbing infrared radiation. The main types of GHGs include carbon dioxide (CO2), methane (CH4), nitrous oxide (N2O), fluorinated gases, and others listed in the Kyoto Protocol.\n\nGHG emissions are categorized into three scopes:\nScope 1: These are direct GHG emissions from sources that the company owns or controls. They typically include emissions from fuel combustion in boilers, vehicles, and industrial processes.\n\nScope 2: These emissions are indirect and result from the consumption of purchased energy, like electricity and heating, which occur at sources owned or controlled by another company.\n\nScope 3: These include all other indirect emissions that occur in the company's value chain, both upstream and downstream. These might involve the emissions from the transportation of goods and services, use of sold products, business travel and more.\n\nTo know and calculate these emissions the process involves these steps in general:\n1. Identification of Emission Sources: Determining all possible sources of GHG emissions related to company's operations.\n\n2. Gathering Activity Data: This involves collecting data on energy consumption, fuel usage, and other operational metrics that impact emissions. This data can often be found in utility bills, receipts, and operational records.\n\n3. Applying Emission Factors: Multiplying the activity data by appropriate emission factors. Emission factors quantify how much GHG is emitted per unit of activity and are often published by governmental bodies or international organizations.", "ghg_scope1": {"tableLabel": "Scope 1", "title": "Scope 1 GHG emissions (tCO2e)", "placeholder": "tCO2e", "description": "Scope 1 GHG emissions are the direct greenhouse gas emissions from sources that are owned or controlled by your company. Examples include emissions from fuel combustion in company-owned equipment, vehicles, and boilers.\n\nIn your company, Scope 1 emissions may arise in areas such as:\nFuel Combustion: This would be from company-owned machinery, vehicles, or heating systems. For instance, emissions from burning natural gas for heating or fuel consumption in company-owned delivery trucks.\nIndustrial Processes: Any direct emissions released during manufacturing or production processes.\nFugitive Emissions: These are leaks of GHGs, such as from refrigerants used in air conditioning systems or emissions from valves and seals in industrial equipment."}, "ghg_scope1_biogenic": {"title": "Scope 1 Biogenic GHG emissions (tCO2e)", "placeholder": "tCO2e", "description": "Scope 1 Biogenic GHG emissions refer to the direct greenhouse gas emissions that come from sources you own or control. These emissions result specifically from biological processes, such as the breakdown of organic material. \n\nExamples of biogenic emissions include:\nEmissions from composting organic waste.\nEmissions from wood combustion for heating or energy, if the biomass is sourced sustainably.\nMethane emissions from livestock.\n\nBiogenic emissions may be prevalent in areas like agricultural operations (e.g., farms with livestock), biofuel production facilities, or any process involving the decomposition of organic materials. "}, "ghg_scope2_locationbased": {"tableLabel": "Scope 2", "title": "Scope 2 GHG emissions (location based) (tCO2e)", "placeholder": "tCO2e", "description": "Scope 2 GHG emissions refer to the indirect greenhouse gas emissions from the consumption of purchased electricity, heat, steam, or cooling. This type of emissions results from the energy consumed by your company but is produced at sources owned or controlled by another company. \n\nLocation-based method calculates emissions based on the average emissions intensity of the grids where the energy consumption occurs, using grid-average emission factor data. In other words, it reflects the typical electricity grid’s emission rates where your company operates.\n\nExamples of where Scope 2 emissions might be covered in your company include:\n- Electricity used for facilities and equipment.\n- Heat used for building heating systems.\n- Cooling processes, such as air conditioning for office spaces."}, "ghg_scope2_marketbased": {"tableLabel": "Scope 2 (Market-based)", "title": "Scope 2 GHG emissions (market based) (tCO2e)", "placeholder": "tCO2e", "description": "Scope 2 GHG emissions refer to the indirect greenhouse gas emissions from the consumption of purchased electricity, heat, steam, or cooling. This type of emissions results from the energy consumed by your company but is produced at sources owned or controlled by another company. \n\nMarket-based approach reflects the contractual arrangements your company might have with energy suppliers and takes into account specific emission factors provided by them. This can include mechanisms like Energy Attribute Certificates or Power Purchase Agreements.\n\nExamples of where Scope 2 emissions might be covered in your company include:\n- Electricity used for facilities and equipment.\n- Heat used for building heating systems.\n- Cooling processes, such as air conditioning for office spaces."}, "ghg_scope1_2_total": {"title": "Scope 1 and 2 emissions total (tCO2e)", "placeholder": "tCO2e", "description": "If  Scope 1 and Scope 2 emissions for your company are not available separately, then here you may provide the total of Scope 1 and Scope 2 GHG emissions together."}, "ghg_total_emissions": {"tableLabel": "Total", "title": "Total GHG Emissions (tCO2e)", "placeholder": "tCO2e", "description": "Total GHG emissions refer to the sum of all direct and indirect greenhouse gas emissions associated with your company's operations. This includes emissions from owned and controlled sources (Scope 1), purchased energy consumption (Scope 2), and the value chain activities (Scope 3). Reporting total emissions provides a comprehensive overview of your company's full climate impact and is key to identifying reduction opportunities."}, "ghg_scope3_total": {"tableLabel": "Scope 3", "title": "Scope 3 GHG emissions (tCO2e)", "placeholder": "tCO2e", "description": "Scope 3 GHG emissions are indirect emissions that occur in the value chain of a company, both upstream and downstream. These emissions are not directly controlled by the company but are a consequence of its activities. For example, Scope 3 emissions include those from the production of purchased goods and services, waste disposal, business travel, and the use of sold products .\n\nScope 3 emissions could be generated in various areas such as the procurement process (emissions from suppliers), logistics (emissions from transporting goods), and product lifecycle (emissions resulting from the usage and disposal of your products)."}, "ghg_scope3_cat_1": {"title": "Scope 3 cat 1: Purchased Goods and Services (tCO2e)", "placeholder": "tCO2e", "description": "Scope 3, Category 1: Purchased Goods and Services, refers to the indirect greenhouse gas (GHG) emissions associated with the production of goods and services that a company purchases. These emissions occur from sources that are not directly owned or controlled by the company but are within its value chain .\n\nExamples of Scope 3 Category 1 emissions include:\n- Emissions from the production of raw materials used by your company.\n- Emissions from the energy used in manufacturing processes of products purchased by your company.\n- Emissions from transportation related to purchased goods."}, "ghg_scope3_cat_2": {"title": "Scope 3 cat 2: Capital Goods (tCO2e)", "placeholder": "tCO2e", "description": "Scope 3 Category 2: Capital Goods refers to greenhouse gas (GHG) emissions associated with the production of purchased capital goods. Capital goods are long-lasting goods that companies use to produce other goods or services. Examples include machinery, equipment, buildings, vehicles, and IT hardware ."}, "ghg_scope3_cat_3": {"title": "Scope 3 cat 3: Fuel- and Energy-Related Activities (tCO2e)", "placeholder": "tCO2e", "description": "Scope 3 Category 3 refers to indirect greenhouse gas (GHG) emissions related to the production of fuels and energy consumed by a company, but not included in Scope 1 (direct emissions from owned or controlled sources) or Scope 2 (indirect emissions from the generation of purchased energy). \n\nExamples of Scope 3 Category 3 activities could include:\nThe extraction, production, and transportation of fuels consumed in generators or vehicles, other than those for which emissions are accounted in Scopes 1 and 2.\nThe transmission and distribution losses of energy consumed, like electricity and steam not included in Scope 2."}, "ghg_scope3_cat_4": {"title": "Scope 3 cat 4: Upstream Transportation and Distribution (tCO2e)", "placeholder": "tCO2e", "description": "Scope 3 Category 4: Upstream Transportation and Distribution refers to the GHG emissions associated with the transportation and distribution of your company's purchased goods and raw materials, which occur before they reach your production facility or operational site. This includes emissions from third-party logistics providers and transportation fleets used in bringing products to your company .\n\nExamples of this might include emissions from:\nTransporting raw materials from suppliers to your manufacturing site.\nDistribution of purchased goods from central warehouses to retail stores if your company operates in the wholesale or retail business.\nShipping and handling of equipment or supplies necessary for your operations."}, "ghg_scope3_cat_5": {"title": "Scope 3 cat 5: Waste Generated in Operations (tCO2e)", "placeholder": "tCO2e", "description": "Scope 3 category 5, known as \"Waste Generated in Operations,\" refers to greenhouse gas emissions resulting from waste management activities such as disposal, treatment, and other actions related to waste generated during a company's operations.\n\nExamples of Waste Generated in Operations include:\nPaper Waste: Offices generating paper waste that is sent for recycling or landfill.\nManufacturing Waste: Scrap metal or defective products in a manufacturing plant.\nFood Waste: Food waste from a company cafeteria or food processing operations.\nPackaging Waste: Waste from packaging materials used during production."}, "ghg_scope3_cat_6": {"title": "Scope 3 cat 6: Business Travel (tCO2e)", "placeholder": "tCO2e", "description": "Scope 3 Category 6: Business Travel involves indirect greenhouse gas (GHG) emissions resulting from travel activities for business purposes. This can include emissions from flights, car rentals, train travel, and hotel stays made by employees for work-related activities ."}, "ghg_scope3_cat_7": {"title": "Scope 3 cat 7: Employee Commuting (tCO2e)", "placeholder": "tCO2e", "description": "Scope 3 Category 7: Employee Commuting refers to the indirect greenhouse gas (GHG) emissions that come from commuting activities by employees of a company. This includes emissions from vehicles used by employees to commute to and from work, whether they use personal cars, public transportation, bicycles, or other modes of transport. Emissions related to the fuel consumption from the vechicles owned by the company are reported within Scope 1."}, "ghg_scope3_cat_8": {"title": "Scope 3 cat 8: Upstream Leased Assets (tCO2e)", "placeholder": "tCO2e", "description": "Scope 3 Category 8: Upstream Leased Assets refers to the greenhouse gas (GHG) emissions associated with the operation of assets that your company leases (but does not own) from other companies. These are upstream in your value chain, meaning they occur from activities that happen before or in support of the operational activities of your company. In example  vehicles or machinery leased from another company."}, "ghg_scope3_cat_9": {"title": "Scope 3 cat 9: Downstream Transportation and Distribution (tCO2e)", "placeholder": "tCO2e", "description": "Scope 3, Category 9 - Downstream Transportation and Distribution refers to the greenhouse gas (GHG) emissions that occur during the transportation and distribution of products after they leave your company. It includes activities like logistics, road transportation using third-party carriers, shipping, and distribution to the end customer.\n\nExamples:\nEmissions from fuel used by third-party trucks transporting your products to retail stores.\nGHGs emitted by ships and airplanes distributing your goods to international markets.\nEmissions from distribution centers not owned by your company but used for storing your products before they reach the customer."}, "ghg_scope3_cat_10": {"title": "Scope 3 cat 10: Processing of Sold Products (tCO2e)", "placeholder": "tCO2e", "description": "Scope 3 Category 10 refers to the \"Processing of Sold Products.\" It includes the emissions generated by processing goods that your company sells but are processed in another facility not owned or controlled by your company. This could include emissions from energy consumption, waste products, or any other processes involved in modifying your sold products at another location. \n\nExamples:\nA furniture company that sells wooden frames which are then treated, painted, or assembled into finished products at another location.\nA cosmetics brand selling raw ingredients to a third-party manufacturer where they are mixed and packaged into final products."}, "ghg_scope3_cat_11": {"title": "Scope 3 cat 11: Use of Sold Products (tCO2e)", "placeholder": "tCO2e", "description": "Scope 3 Category 11 refers to greenhouse gas (GHG) emissions from the use of the products sold by a company. It includes the emissions associated with how the company's products are used by the end users.\n\nExamples could be:\nEmissions from fuel consumption in vehicles sold.\nEmissions from energy consumed by electronic devices sold after they are deployed by consumers.\nEmissions from heating systems in homes using equipment or appliances sold by the company."}, "ghg_scope3_cat_12": {"title": "Scope 3 cat 12: End-of-Life Treatment of Sold Products (tCO2e)", "placeholder": "tCO2e", "description": "Scope 3 Category 12, \"End-of-Life Treatment of Sold Products,\" refers to the greenhouse gas (GHG) emissions associated with the disposal of products by consumers at the end of their life cycle. This includes emissions from waste treatment processes such as landfilling, recycling, incineration, and composting.\n\nExamples:\nAppliances: Emissions from refrigerants released when a refrigerator is disposed of.\nElectronics: Emissions from recycling or landfilling of used electronic devices.\nPackaging: Emissions from the breakdown of packaging materials when they are not recycled."}, "ghg_scope3_cat_13": {"title": "Scope 3 cat 13: Downstream Leased Assets (tCO2e)", "placeholder": "tCO2e", "description": "Scope 3, Category 13: Downstream Leased Assets refers to emissions from the operation of assets that are owned by your company but leased to others. These emissions are considered indirect as they occur from downstream activities related to the company's value chain.\n\nExamples of downstream leased assets can include buildings owned by your company but leased out to tenants or fleets of vehicles leased to customers."}, "ghg_scope3_cat_14": {"title": "Scope 3 cat 14: Fran<PERSON>ses (tCO2e)", "placeholder": "tCO2e", "description": "Scope 3 Category 14: Franchises refers to the greenhouse gas emissions associated with companies that operate under your brand as franchises. This includes all indirect emissions—such as energy use, transportation, or waste—that occur in these franchise operations, even though they might not be directly owned or controlled by your company."}, "ghg_scope3_cat_15": {"title": "Scope 3 cat 15: Investments (tCO2e)", "placeholder": "tCO2e", "description": "Scope 3 category 15: Investments refers to the indirect greenhouse gas (GHG) emissions that occur because of an organization's investments. This involves emissions from the entire life cycle of the investments that are not already reported in other categories. It is part of the broader Scope 3 emissions which occur from sources not directly owned or controlled by a company but related to its activities.\n\nExamples:\nEmissions from companies in which the organization holds shares or bonds.\nEmissions associated with financial support provided to projects or businesses.\nIndirect emissions from investment properties or assets."}}, "ghg_reduction": {"title": "GHG Reduction and Climate Transition", "description": "Does your company have emissions reduction goals? If yes, please specify.", "helpText": "Emissions reduction goals are about stating whether your business has set specific targets to reduce greenhouse gas (GHG) emissions. If yes, you need to specify those targets.\n\nFor example:\nAbsolute Reduction Targets: Clear, quantitative goals to reduce emissions by a certain amount (e.g., aiming to reduce Scope 1 and Scope 2 emissions by 1000 tCO2 by 2030 from a 2010 baseline).", "if_ghg_reduction_goals": {"title": "Reduction goals", "placeholder": "Answer", "description": "Emissions reduction goals are about stating whether your business has set specific targets to reduce greenhouse gas (GHG) emissions. If yes, you need to specify those targets.\n\nIn example:\n\nAbsolute Reduction Targets: Clear, quantitative goals to reduce emissions by a certain amount (e.g., aiming to reduce Scope 1 and Scope 2 emissions by 1000 tCO2 by 2030 from a 2010 baseline)."}, "ghggoal": {"title": "GHG Reduction Goals", "addItemLabel": "GHG Goal", "items": {"ghggoal_element": {"title": "Target scope", "placeholder": " Select Answer"}, "ghggoal_base_year": {"title": "Base year", "placeholder": "(e.g. 2023)", "description": "The \"Base year\" is the specific year against which a company's current greenhouse gas (GHG) emissions are compared to measure progress in GHG reduction efforts. It should be a recent and representative year for which you have verifiable data on your company's GHG emissions."}, "ghggoal_target_year_st": {"title": "Target year Short Term", "placeholder": "(e.g. 2050)", "description": "The \"Target Year\" refers to a specific future year by which a company aims to achieve its set GHG emission reduction targets. "}, "ghggoal_goal_numeric_st": {"title": "Short Term Value", "placeholder": "Enter short term value", "description": "The \"Target Year\" refers to a specific future year by which a company aims to achieve its set GHG emission reduction targets. "}, "ghggoal_target_year_lt": {"title": "Target year Long Term", "placeholder": "(e.g. 2030)", "description": "The \"Target Year\" refers to a specific future year by which a company aims to achieve its set GHG emission reduction targets. "}, "ghggoal_goal_numeric_lt": {"title": "Long Term Value", "placeholder": "Enter long term value", "description": "The \"Target Year\" refers to a specific future year by which a company aims to achieve its set GHG emission reduction targets. "}}}, "ghggoal_narrative": {"title": "Other", "placeholder": "Please specify", "description": "In case your company's GHG reduction goals cannot be presented within the scope of above table then please describe them here."}}, "transition_plan": {"title": "Transition Plan", "description": "Does your company have a plan to reduce your environmental impact over time (transition plan)? If yes, please specify.", "helpText": "A transition plan is a strategic document outlining how your company intends to reduce its environmental impact over time, specifically its greenhouse gas (GHG) emissions. The plan sets out future actions to align your business operations with global climate goals, like limiting global warming to 1.5°C. It often includes setting measurable GHG emission reduction targets, integrating these targets into the business strategy, and monitoring progress regularly.\n\nA transition plan details how a company will transition its operations to reduce emissions.\n\nExamples of actions in a transition plan could include:\n- Transitioning to electric vehicles for company transportation.\n- Replacing non-renewable energy usage with renewable sources.\n- Updating equipment and processes for higher energy efficiency.\n- Developing sustainable products or services.", "if_transition_plan_existence": {"title": "Transition plan", "placeholder": "Answer", "description": "A transition plan is a strategic document outlining how your company intends to reduce its environmental impact over time, specifically its greenhouse gas (GHG) emissions. The plan sets out future actions to align your business operations with global climate goals, like limiting global warming to 1.5°C. It often includes setting measurable GHG emission reduction targets, integrating these targets into the business strategy, and monitoring progress regularly.\n\nTransition plan details how a company will transition its operations to reduce emissions.\n\nExamples of actions in a transition plan could include:\n\n- Transitioning to electric vehicles for company transportation .\n- Replacing non-renewable energy usage with renewable sources .\n- Updating equipment and processes for higher energy efficiency .\n- Developing sustainable products or services"}, "transition_plan_description": {"title": "Transition plan description", "placeholder": "Please specify", "description": "A transition plan description outlines the actions your company will take to move towards a more sustainable and low-carbon economy. This plan is guided by broader goals, like aligning with global efforts to limit warming to 1.5°C, and includes both immediate and future steps. Components often include setting specific greenhouse gas (GHG) reduction targets, identifying key responsibilities, integrating the plan into business strategy and financial planning, and detailing decarbonization levers and pathways with indicators that can be regularly reviewed and updated  .\n\nExamples of elements that might be included in the transition plan are:\nImplementing energy efficiency measures\nSwitching to renewable energy\nDeveloping sustainable products or services"}}, "physical_risks_from_climate_change": {"title": "Physical Risks From Climate Change", "description": "Have you identified any environmental or climate-related physical risks that could affect your business? If yes, please specify.", "helpText": "The climate-related physical risks can be classified into two main types:\n\n**Acute Physical Risks:** These are event-driven risks that arise from specific weather-related events such as storms, floods, fires, or heatwaves. For example, if your business operates in an area prone to flooding, a major flood could damage your infrastructure, disrupt supply chains, or cause loss of inventory.\n\n**Chronic Physical Risks:** These emerge from long-term shifts in climate patterns, such as increasing average temperatures, rising sea levels, or changing precipitation patterns. For instance, if your business depends on a stable water supply, a gradual reduction in water availability due to climate change could affect your operations.\n\nExamples of how these risks might affect a business include:\n- Supply chain disruptions due to extreme weather events.\n- Increased cooling costs from rising temperatures.\n- Loss of agricultural productivity affecting raw materials supply if your business is reliant on agriculture.", "if_physical_risk_identification": {"title": "Physical risk identification", "placeholder": "Answer", "description": "The climate-related physical risks can be classified into two main types:\n\nAcute Physical Risks: These are event-driven risks that arise from specific weather-related events such as storms, floods, fires, or heatwaves. For example, if your business operates in an area prone to flooding, a major flood could damage your infrastructure, disrupt supply chains, or cause loss of inventory.\n\nChronic Physical Risks: These emerge from long-term shifts in climate patterns, such as increasing average temperatures, rising sea levels, or changing precipitation patterns. For instance, if your business depends on a stable water supply, a gradual reduction in water availability due to climate change could affect your operations.\n\nExamples of how these risks might affect a business include:\n\nSupply chain disruptions due to extreme weather events.\n\nIncreased cooling costs from rising temperatures.\n\nLoss of agricultural productivity affecting raw materials supply if your business is reliant on agriculture ."}, "physical_risk_identification": {"title": "Physical risk identification description", "placeholder": "Please specify", "description": "This question will seek answer to how does your company identify climate-related physical risks.\n\nAn example of description might be:\nOur company has assessed potential physical climate risks that may affect our operations in the short-, medium- and long-term. We operate a single manufacturing facility in southern Spain, which is increasingly vulnerable to high temperatures, droughts, and wildfires.\n\nWe identified the following key physical hazards:\nHeatwaves and rising average temperatures, which could impact worker health and energy consumption.\nDrought risk, affecting the availability and cost of water used in our wood treatment processes.\nWildfire exposure, due to proximity of our site to forested areas.\n\nTo identify these risks, we:\nReviewed historical climate data from the Meteorological Agency.\nConsulted the IPCC climate scenarios to consider future projections.\nParticipated in a local business chamber climate adaptation seminar.\nPerformed a location-based sensitivity mapping exercise using public EU Climate-ADAPT tools."}, "climate_change_adaptation_actions": {"title": "Climate change adaptation actions", "placeholder": "Please specify", "description": "Climate change adaptation actions involve adjusting processes, policies, and practices to minimize the risks posed by climate change and to take advantage of any potential opportunities it brings. These actions are intended to help a company become more resilient to both current and future changes in climate conditions.\n\nExamples of climate change adaptation actions include:\n\nInfrastructure Improvements: Updating buildings and facilities to withstand extreme weather events, such as floods or storms. This might involve improving drainage systems or reinforcing structures against high winds.\n\nWater Management: Implementing measures to manage water resources efficiently during periods of drought or increased rainfall. This could involve rainwater harvesting or upgrading irrigation systems.\n\nDiversifying the Supply Chain: Sourcing materials or services from multiple suppliers in different geographic locations to reduce dependency on areas highly vulnerable to climate impacts.\n\nDeveloping New Products: Creating products that are designed to perform well under future climate conditions, such as heat-resistant crops in agriculture.\n\nCommunity Engagement: Working with local communities to develop joint measures for climate adaptation, which could include awareness programs or infrastructure projects to reduce vulnerability."}, "insurance_coverage": {"title": "Insurance coverage (€)", "placeholder": "€", "description": "Insurance coverage in monetary value whereby insurance company provides a guarantee of compensation for your company to mitigate risks associated with physical damages due to climate change, such as damages from floods, wildfires, or storms.\n\nExamples of insurance coverage relevant to climate change might include:\nProperty insurance that covers damages from natural disasters caused or exacerbated by climate change.\nBusiness interruption insurance that provides compensation for losses due to climate-related disruptions.\nLiability insurance that covers potential damages a company could cause to third parties due to climate impacts."}, "physical_risk_effect": {"title": "Physical climate risk effect", "placeholder": "Please specify if and how physical climate conditions may impact to your business", "description": "This focuses on the potential and anticipated effects of climate-related risks, including but not limited to currently observed impacts that can have impact on your company's operations and assets. \n\nTo describe the physical risk effects your company might experience, you should consider identifying specific climate-related events or conditions that can have impact to your operations. This might include damage to infrastructure from floods, heatwaves affecting productivity, or supply chain disruptions due to extreme weather.\n\nExample answer:\nThe primary physical climate risk affecting our business is the increased frequency and intensity of droughts. This is already contributing to lower grape yields and higher irrigation costs, especially during the summer months.\n\nIn the medium term (3–10 years), projections suggest increased soil degradation and reduced water availability, which may:\nIncrease the cost of raw materials (grapes),\nDisrupt harvest timing and processing schedules,\nPotentially lower the quality of our wine, affecting revenue.\n\nAdditionally, our warehouse is located in a moderate flood-risk area, which, if affected, could cause damage to stored inventory and packaging equipment, disrupting deliveries and increasing insurance costs.\n\nWe estimate that, under a high-emissions climate scenario (IPCC SSP5-8.5), extreme heat and irregular rainfall could result in up to 20% reduction in production capacity by 2035, if no adaptation measures are taken."}}}, "managing_environmental_resources": {"title": "Managing Environmental Resources", "pollution": {"helpText": "Whether your company is required by law or other competent authorities to report on pollution.\n\nDepending on your operations authorities may require that companies report:\n- Emissions of pollutants to air, such as sulfur oxides (SOx), nitrogen oxides (NOx), and particulate matter.\n- Discharges to water, like chemical spillages or waste water.\n- Contaminants affecting soil quality.\n\nCompanies engaged in industrial activities, like manufacturing or large-scale agriculture, often have these obligations.", "title": "Pollution", "description": "Is your company required by law or other competent authorities to report on pollution? If yes, please disclose which matters your report:", "if_pollution_reporting": {"title": "Pollution reporting", "placeholder": "Answer", "description": "Whether your company is required by law or other competent authorities to report on pollution.\n\nDepending on your operations authorities may require that companies report :\n\n- Emissions of pollutants to air, such as sulfur oxides (SOx), nitrogen oxides (NOx), and particulate matter.\n\n- Discharges to water, like chemical spillages or waste water.\n\n- Contaminants affecting soil quality.\n\nCompanies engaged in industrial activities, like manufacturing or large-scale agriculture, often have these obligations."}, "if_pollution_to_air": {"title": "Pollution to air", "placeholder": "Answer", "description": "Pollution to air refers to harmful substances released into the atmosphere, which can negatively affect human health and the environment. Common pollutants include sulphur oxides (SOx), nitrogen oxides (NOx), particulate matter (PM10), and volatile organic compounds (VOCs), among others  .\n\nExamples of activities that produce air pollution in a company could be:\nElectricity Generation: Using fossil fuels or biomass, which results in emissions like SOx and NOx .\nFuel Combustion: This occurs in industrial operations or when using machinery, producing CO and particulate matter .\nTransport: Vehicle emissions from company fleets, affecting air quality through pollutants like particulate matter or NOx .\nIndustrial Processes: Non-combustion-related processes can also emit various pollutants ."}, "if_pollution_to_water": {"title": "Pollution to water", "placeholder": "Answer", "description": "Water pollution refers to the contamination of water bodies (such as lakes, rivers, oceans, and groundwater) caused by harmful substances. Pollutants can be physical, chemical, or biological substances that degrade the quality of water, making it detrimental to the environment and human health.\n\nExamples of Water Pollution:\nNutrients: Excessive nitrogen and phosphorus from agricultural runoff can lead to eutrophication, causing algal blooms that deplete oxygen in water.\nHeavy Metals: Discharges from industrial operations may release metals like cadmium, mercury, lead, causing toxicity to aquatic life and humans.\nPesticides and Herbicides: Used in agriculture, these chemicals can run off into water bodies, affecting aquatic organisms.\nIndustrial Waste: Chemicals, pollutants, and untreated wastewater from industrial activities can pollute water sources.\nVolatile Organic Compounds (VOCs): These include substances like benzene and toluene, which might enter water systems through industrial spillages."}, "if_pollution_to_soil": {"title": "Pollution to soil", "placeholder": "Answer", "description": "Pollution to soil refers to the contamination of soil by harmful substances, which can degrade its quality and disrupt its natural processes. Examples of pollutants typically include nitrogen (N), phosphorus (P), heavy metals (e.g., from land application of sewage sludge), volatile organic compounds (VOCs), persistent organic pollutants (POPs), and pesticides.\n\nIn a company, sources of soil pollution could stem from several activities such as:\nIndustrial processes like chemical production or textiles manufacturing.\nAccidental spills of petrol-derived products.\nAgricultural activities like irrigation with untreated wastewater or poultry rearing."}}, "waste_management": {"title": "Resource Use, Circularity and Waste", "description": "Please provide information about waste management, recycling and use of materials:", "generation_of_waste": {"title": "Generation of waste (t)", "placeholder": "t", "description": "The generation of waste refers to the total annual amount of waste produced by a company.\n\nExamples of waste generation include:\nNon-hazardous waste, like office paper, cardboard, and packaging materials.\nHazardous waste, such as used oils, batteries, pesticides, mercury-containing equipment, and fluorescent lamps.\n\nThe generation of waste can be covered in areas such as production processes, office operations, or any region where materials are consumed or disposed of. For instance, manufacturing departments often generate specific waste types like used lubricants in the manufacturing sector."}, "recycling_or_reuse": {"title": "Recycling or reuse (t)", "placeholder": "t", "description": "Recycling or reuse involves the process of transforming waste materials into new products or materials, thereby extending their lifecycle and reducing waste. Recycling includes reprocessing waste materials into new products, while reuse is the practice of using items again for the same or a different purpose.\n\nActivities could include:\nRecycling: Collecting and processing materials like paper, plastic, and metal to create new products. For instance, recycling plastic bottles into new plastic items.\nReuse: Utilizing items more than once. This could range from using glass jars for storage to repurposing office furniture.\n\nPlease indicate the total mass-volume amount of recycled and/or reused waste. "}, "use_of_materials": {"title": "Use of materials", "placeholder": "List the main materials you use and quantities, if available.", "description": "This refers to the inputs, the raw or processed materials a company consumes to produce goods or services. This is particularly relevant for companies in manufacturing, construction, or packaging sectors.\n\nFor example a construction company might report:\nConcrete: 1,200 tonnes\nWood: 150 tonnes\nSteel: 80 tonnes"}}, "company_water_use": {"title": "Water Use", "description": "Please provide information about your company’s water use:", "water_withdrawal": {"title": "Water withdrawal (t)", "placeholder": "t", "description": "Water withdrawal is the amount of water taken by a company from any source into its boundaries during a reporting period. This includes water sourced from public water supply networks, groundwater from wells, rivers, lakes, and even rainwater if collected and stored.\n\nExamples of Water Withdrawal: \nA company using water from the public mains for daily operations.\nAn agricultural company collecting rainwater for irrigation.\nA manufacturing plant drawing water from a nearby river for cooling processes.\nAny agrarian or garden management that involves irrigation systems."}, "water_withdrawal_in_area_of_high_water_stress": {"title": "Water withdrawal in area of high water stress (t)", "placeholder": "t", "description": "Water withdrawal in areas of high water stress refers to the extraction of water by an organization from natural sources (like rivers, lakes, or groundwater) in regions where water resources are limited compared to the demand for them. A high water stress area is characterized by a high ratio of water usage to water availability, typically marked by certain thresholds such as the baseline water stress indicator values above 40%. \n\nExamples of high water stress areas include dry regions where water is scarce and heavily utilized for agriculture, industries, or by urban populations. \n\nIn your company, this may be relevant in contexts such as operations involving agricultural production with irrigation needs, manufacturing processes requiring significant water use, or operations in regions identified by tools like the WRI’s Aqueduct Water Risk Atlas https://www.wri.org/applications/aqueduct/water-risk-atlas/. \n\nIt's important to identify if your operations are situated in such areas and then manage and report the water withdrawals appropriately. "}, "water_consumption": {"title": "Water consumption (t)", "placeholder": "t", "description": "Water consumption refers to the amount of water that is taken into a company's operations and not returned to the water environment or another party. It accounts for water used in various processes within the company boundaries that might not be released back, such as water lost through evaporation during a manufacturing process or water retained in products.\n\nExamples of water consumption include:\nWater used and evaporated in cooling.\nWater incorporated into products, such as beverages in a bottling plant.\nWater used for irrigation in company landscapes."}}, "biodiversity": {"title": "Biodiversity", "description": "Is any of your land or facility located near areas important for biodiversity? If yes, please provide details:", "helpText": "The question about whether any of your land or facilities are located near areas important for biodiversity is aimed at identifying any potential impact your operations may have on ecologically significant areas.\n\nSuch areas, called biodiversity-sensitive areas, might include designated national parks, protected sanctuaries, or regions recognized for their unique wildlife or plant species. Being located near such areas could imply that your operations might affect local biodiversity, either positively or negatively.\n\nIt's helpful to refer to official databases or maps of protected areas to determine this information, such as those provided by organizations like the Natura 2000 network, the World Database on Protected Areas or National Environment Agency.", "if_land_near_sensitive_areas": {"title": "Land near biodiversity sensitive areas", "placeholder": "Answer", "description": "The question about whether any of your land or facilities are located near areas important for biodiversity is aimed at identifying any potential impact your operations may have on ecologically significant areas. \n\nSuch areas, called biodiversity-sensitive areas, might include designated national parks, protected sanctuaries, or regions recognized for their unique wildlife or plant species. Being located near such areas could imply that your operations might affect local biodiversity, either positively or negatively.\n\nIt's helpful to refer to official databases or maps of protected areas to determine this information, such as those provided by organizations like the Natura 2000 network, the World Database on Protected Areas or National Environment Agency."}, "land_area_near_biodiversity": {"title": "Total land area (ha)", "placeholder": "ha", "description": "The complete surface area owned, leased, or managed by the company. This would give an overview of the geographical footprint of the company.\n\nInclude:\n- Owned land\n- Leased land\n- Managed land\n- All geographical locations (offices, factories, warehouses, etc.)\n- All land types (urban, rural, natural)\n\nExlude:\n- Land not under control of the company (e.g. suppliers’ land)\n- Unused land not under legal title"}, "total_use_of_land": {"title": "Total use of land (ha)", "placeholder": "ha", "description": "The activities or functions for which the land is being used (e.g. agriculture, industrial operations, infrastructure).\nIt is more functional and impact-oriented, focusing on how much of the land is used.\n\nInclude:\nAreas used for production (e.g. farming, mining)\nBuilt-up areas (e.g. buildings, parking)\nInfrastructure (roads, pipelines)\nLand conversion (natural to artificial use)\nLand-use categories (agriculture, forestry, urban use, etc.)\n\nExclude:\nUnused or idle land with no active purpose\nLeased land used by third parties for unrelated activities\nLand under conservation (if not actively altered by company)"}, "total_sealed_area": {"title": "Total sealed area (ha)", "placeholder": "ha", "description": "Total sealed area refers to spaces where the natural soil has been covered, resulting in an impermeable surface. This can affect the environment by altering natural water infiltration and increasing runoff. Examples of sealed areas include roads, parking lots, and buildings."}, "total_nature_oriented_area_on_site": {"title": "Total nature-oriented area on-site (ha)", "placeholder": "ha", "description": "Total nature-oriented area on-site refers to the areas within your organization's site dedicated primarily to nature preservation or restoration. These areas are designed to promote biodiversity and can include elements like green roofs, green walls, gardens, natural landscaping, and water drainage areas designed to support plants and wildlife.\n\nNatural or semi-natural spaces located within the premises owned, leased, or managed by the company.\n\nExamples of nature-oriented areas could be:\nGreen Roofs or Walls: Parts of your building covered with vegetation.\nOn-site Gardens: Areas with native plants that support local wildlife and help preserve biodiversity.\nWater Drainage Systems: Natural water runoff designs or small ponds that support aquatic life.\nNatural Landscaping: Portions of your grounds that are maintained in a natural state, rather than manicured lawns.\n\nThis exludes:\nExternal areas, even if owned but not adjacent or integrated into site operations\nAreas not designed or managed for nature conservation"}, "total_nature_oriented_area_off_site": {"title": "Total nature-oriented area off-site (ha)", "placeholder": "ha", "description": "Total nature-oriented area off-site refers to areas outside the organization's premises that are dedicated to nature preservation or restoration, promoting biodiversity. \n\nInclude areas supported, financed, or maintained by the company but located outside its operational sites.\nExlude areas located within the premises of the company. \n\nExamples of total nature-oriented area off-site could include:\nA plot of land managed by your company in the countryside, developed as a natural habitat for local species.\nPartnerships with local conservation projects where part of the land is managed by your company to enhance biodiversity.\nGreen corridors or buffer zones that your company helps maintain outside of its premises but still within areas it influences or manages."}}}, "workforce_social_responsibility": {"title": "Workforce & Social Responsibility", "workforce_general_characteristics": {"description": "Please provide details about the employee characteristics for your company", "title": "Workforce - General Characteristics", "nr_employees": {"title": "Number of employees", "placeholder": "Enter number of employees", "description": "The total number of people employed by the company. This can be reported in terms of headcount or full-time equivalent (FTE) positions. Headcount refers to the total number of employees, while FTE is calculated by equating the number of hours worked by all employees to the work hours of full-time employees.\n\nWhen reporting, the count should include all types of employment contracts, whether temporary or permanent. Generally, you should exclude non-employees like freelancers or third-party contractors unless they are exclusively working for your company under similar conditions as employees."}, "nr_employees_gender_m": {"title": "Number of male employees", "placeholder": "Enter number of male employees", "description": "Total number of male employees"}, "nr_employees_gender_f": {"title": "Number of female employees", "placeholder": "Enter number of female employees", "description": "Total number of female employees"}, "nr_employees_gender_other": {"title": "Number or employees of other gender", "placeholder": "Enter number of employees of other gender", "description": "Total number of employees of other gender"}, "nr_temporary_employees": {"title": "Temporary employees", "placeholder": "Enter number of temporary employees", "description": "Temporary employees refer to individuals who have a temporary employment contract with your organization. This could be for a fixed term or to cover specific periods such as seasonal peaks or maternity leaves. \n\nWhen reporting, include all those with temporary contracts under the category of temporary employees. Generally, exclude casual workers or self-employed contractors unless they have a specific arrangement that categorizes them similarly to employees. \n\nTypically, temporary workers provided by other companies specifically engaged in employment activities can also be included, especially when there's a significant or increasing reliance on them."}, "nr_permanent_employees": {"title": "Permanent employees", "placeholder": "Enter number of permanent employees", "description": "Permanent employee is an individual who is in an employment relationship with the company under a contract that does not have a predefined end date. Such employees typically have regular hours, receive benefits such as health insurance and retirement plans, and are entitled to employment protections under national law or practice. \n\nWhen counting permanent employees you should include all individuals who meet the above criteria, covering both full-time and part-time workers, as long as their employment is permanent. Exclude temporary or contract-based workers who are only engaged for a specific period or task, as they do not fall under the definition of permanent employment."}, "employee_turnover_rate": {"title": "Employee turnover rate (%)", "placeholder": "Enter employee turnover rate", "description": "The Employee Turnover Rate is a measure of how frequently employees leave a company during a specific period. It typically includes employees who leave voluntarily, are dismissed, retire, or pass away while in service .\n\nTo calculate the turnover rate, use the formula:\nTurnover Rate = (Number of employees who left during the year)/(Average number of employees during the year) x 100\n\nThis calculation includes all departures, regardless of their reason, except for temporary leaves or transfers within the company. So, exclude cases like sabbaticals or internal job transfers when calculating this rate."}, "nr_non_employees": {"title": "Number of non-employees", "placeholder": "Enter number of non-employees", "description": "The non-employees refer to individuals who are not directly employed by your company but who provide labor to it. This includes two main categories:\n\nSelf-Employed Individuals: These are workers who have contracts with your company to supply labor without being formal employees.\nTemporary Workers: These are people provided by agencies or undertakings primarily engaged in employment activities.\n\nFocus on individuals like freelancers, subcontractors and agency workers involved in your operations."}}, "workforce_remuneration_collective_bargaining_and_training": {"title": "Workforce - Remuneration, Collective Bargaining and Training", "gender_paygap": {"title": "Gender paygap (%)", "placeholder": "Enter gender pay gap", "description": "The gender pay gap refers to the difference in average pay levels between female and male employees, expressed as a percentage of the average pay level of male employees. It is a key metric for assessing gender equality in the workplace, specifically in terms of compensation. The calculation includes all employees, with separate average pay calculations for female and male employees.\n\nItems to include in the calculation:\n- Base salary, cash allowances, bonuses, commissions, and other forms of variable cash payments.\n- Benefits in kind, such as cars, private health insurance, and life insurance.\n- Direct remuneration, which is the sum of all cash and non-cash benefits.\n\nIf your company's workforce is below 150 employees, you may omit this disclosure.  "}, "employees_covered_cb": {"title": "Employees covered by CB (%)", "placeholder": "Enter number of employees covered by collective bargaining", "description": "Employees covered by Collective Bargaining Agreement (CB) refers to individuals to whom the terms of a collective bargaining agreement apply. This can cover both union and non-union employees when the agreement extends its terms to a broader group. It's important to only include those employees directly covered by such agreements, and not to confuse this with overall union membership or representation by works councils.\n\nExclude counts of employees not under these collective bargaining agreements, even if they are union members or represented by a work council not associated with a collective agreement. The percentage of employees covered by these agreements can differ from those merely being part of a union."}, "trainings_gender_m": {"title": "Training hours (M)", "placeholder": "Enter training hours for male employees", "description": "The average number of annual training hours per male employee. \n\nIt includes initiatives put in place by the company that aim at maintaining or improving the skills and knowledge of its workers. This can involve various methodologies like on-site training or online courses .\n\nWhen calculating this metric, ensure you include structured learning activities such as workshops, seminars, or e-learning programs that are organized and recognized by the company. Exclude informal learning opportunities such as unstructured on-the-job training or self-study that doesn't utilize company resources or formal recognition."}, "trainings_gender_f": {"title": "Training hours (F)", "placeholder": "Enter training hours for female employees", "description": "The average number of annual training hours per female employee. \n\nIt includes initiatives put in place by the company that aim at maintaining or improving the skills and knowledge of its workers. This can involve various methodologies like on-site training or online courses .\n\nWhen calculating this metric, ensure you include structured learning activities such as workshops, seminars, or e-learning programs that are organized and recognized by the company. Exclude informal learning opportunities such as unstructured on-the-job training or self-study that doesn't utilize company resources or formal recognition."}, "if_minimum_wage_pay": {"title": "Do all employees of your company receive pay that is equal or above applicable minimum wage as defined in respective country", "placeholder": "Answer", "description": "Does your company pay all employees at least the minimum wage set by the law in the respective country? This amount is often determined by national laws or collective bargaining agreements.\n\nIn example:\n\nIf you operate in a country where the minimum wage is €10 per hour, then all your employees must receive at least this amount for every hour they work.\n\nIf a collective bargaining agreement in your industry sets a minimum wage of €12 per hour, your employees must be paid accordingly at a minimum.\n\nEnsuring payment at or above the minimum wage means adhering to these legal standards to prevent underpayment. Organizations often check their payroll records against these benchmarks to ensure compliance.\n\npolicies regarding minimum wage pay might be detailed in employee handbooks, HR policies and procedures, or collective bargaining agreements if applicable. These documents specify compliance with national wage laws and any additional company-specific wage practices."}}, "workforce_health_safety": {"title": "Workforce - Health and Safety", "work_accidents_employees": {"title": "Work accidents (employees)", "placeholder": "Enter number of work accidents", "description": "Work accidents refers to recordable events that occur while an employee is engaged in occupational activities at work or during work time, resulting in physical or mental harm.\n\nThis category includes events that lead to death, days away from work, restrictions, transfers to other jobs, medical treatment beyond first aid, or loss of consciousness. \n\nExclusions typically involve injuries that do not require medical treatment beyond first aid, as these are generally not recordable. Additionally, the classification of commuting accidents as work-related depends on national legislation and may vary across countries, meaning they may or may not be included. Mental illness is considered work-related only if it is linked directly to job performance or workplace factors.\n\nThis apply to employees only, non-employees are not included."}, "work_fatalities_employees": {"title": "Fatalities (employees)", "placeholder": "Enter number of fatalities", "description": "Fatalities refer to the number of deaths resulting from work-related injuries and work-related ill health. This includes any fatal incidents that occur due to hazards encountered during routine work activities, which have been confirmed as work-related by legal or healthcare professionals .\n\nWhat to Include:\nFatalities due to work-related injuries.\nFatalities due to work-related ill health.\nFatalities occurring during travel for work if the activities were in the employer's interest.\n\nWhat to Exclude:\nFatalities occurring while commuting to and from work, unless your national regulation defines these as work-related.\nFatalities caused by factors not directly related to work, such as health problems from lifestyle choices not connected to work .\n\nThis apply to employees only, non-employees are not included."}}}, "business_ethics_governance": {"title": "Business Ethics & Governance", "incidents_related_severe_human_rights": {"title": "Incidents Related to Severe Human Rights", "nr_human_rights_violations": {"title": "Human rights violations", "placeholder": "Enter number of human rights violations", "description": "Incidents or complaints related to severe breaches of human rights within your company over the past year. These concern the own workforce.\n\nExamples of such violations could be:\n\nForced labor: Making people work against their will under threat.\n\nDiscrimination: Unequal treatment of employees based on gender, race, or other characteristics.\n\nUnsafe working conditions.\n\nThese incidents could relate to several areas:\n\n- Recruitment and employment practices.\n\n- Workplace policies and implementation.\n\n- Grievance and complaint mechanisms where employees or stakeholders can report issues."}, "nr_human_rights_stakeholders_cases": {"title": "Incidents communities", "placeholder": "Enter number of community incidents", "description": "Incidents related to clients, workers of your suppliers, or affected communities - it refers to severe negative human rights incidents with your stakeholders or supply chain. Examples could include issues like child labor, forced labor, human trafficking, discrimination, or any other severe human rights violation that affects any stakeholders such as clients, suppliers' workers, or the communities impacted by your business activities.\n\nThis does not include incidents with own workforce."}}, "corruption_and_bribery": {"title": "Corruption and Bribery", "helpText": "Whether your company has faced any legal actions or penalties due to issues surrounding dishonest or unethical practices involving bribery or corruption. How many cases?\n\nBribery involves giving or receiving something of value to influence a transaction or decision, which is illegal and unethical.\nFraud refers to wrongful or criminal deception intended to result in financial or personal gain.\nCorruption is the abuse of entrusted power for private gain, including practices like facilitation payments, extortion, and money laundering.\n\nYou may get help from your:\n- Legal Department or Compliance Office: to see if there are records or ongoing issues related to legal cases or fines.\n- Corporate Governance Reports: such issues might be documented in governance reports or meeting records of board meetings.\n- Financial Statements: occasionally, financial statements will include notes about such legal liabilities.", "description": "Has your company received any fines or been involved in legal cases related to bribery, fraud, or corruption?", "if_abc_convictions": {"title": "ABC Convictions", "placeholder": "Answer", "description": "Whether your company has faced any legal actions or penalties due to issues surrounding dishonest or unethical practices involving bribery or corruption. How many cases?\n\nBribery involves giving or receiving something of value to influence a transaction or decision, which is illegal and unethical.\n\nFraud refers to wrongful or criminal deception intended to result in financial or personal gain.\n\nCorruption is the abuse of entrusted power for private gain, including practices like facilitation payments, extortion, and money laundering.\n\nYou may get help from your:\n\nLegal Department or Compliance Office: to see if there are records or ongoing issues related to legal cases or fines.\n\nCorporate Governance Reports: such issues might be documented in governance reports or meeting records of board meetings.\n\nFinancial Statements: occasionally, financial statements will include notes about such legal liabilities."}, "nr_abc_convictions": {"title": "Number of convictions", "placeholder": "Enter number of convictions", "description": "The total number of convictions for violations of anti-corruption and anti-bribery laws.\n\nConvictions refer to verdicts by a criminal court for offenses related to corruption and bribery (e.g., those recorded in a criminal record within an EU Member State)​."}, "abc_fines": {"title": "Fines (€)", "placeholder": "Enter fines", "description": "Total monetary value of fines related to violations of anti-corruption and anti-bribery laws."}}, "workforce_characteristics": {"title": "Workforce Characteristics (Board)", "management_board_f": {"title": "Management board members (F)", "placeholder": "Enter Management board members (F)", "description": "Number of female members in the Management Board of your company."}, "management_board_m": {"title": "Management board members (M)", "placeholder": "Enter Management board members (M)", "description": "Number of male members in the Management Board of your company."}}}}, "ghgCalculator": {"title": "GHG Emissions Calculator", "description": "Calculate your organization's greenhouse gas emissions for {year}", "backToOverview": "Back to Overview", "scope1Emissions": "Scope 1 Emissions", "scope2Emissions": "Scope 2 Emissions", "totalEmissions": "Total Emissions", "scope1Description": "Direct emissions from owned sources", "scope2Description": "Indirect emissions from purchased energy", "totalDescription": "Combined Scope 1 and Scope 2 emissions", "save": "Save", "saving": "Saving...", "resetAll": "Reset", "resetConfirm": "Are you sure you want to reset the form data? This action cannot be undone.", "resetSuccess": "Data has been reset successfully", "saveSuccess": "Data saved successfully", "saveError": "Failed to save data. Please try again.", "generateReport": "Generate Report", "calculateEmissions": "Calculate Emissions", "emissionsHistory": "Emissions History", "noEmissionsData": "No emissions data", "noEmissionsDescription": "Get started by calculating your emissions", "year": "Year", "scope1EmissionsTable": "Scope 1 Emissions", "scope2EmissionsTable": "Scope 2 Emissions", "totalEmissionsTable": "Total Emissions", "actions": "Actions", "edit": "Edit", "viewReport": "View Report", "noDataForReport": "No data available to generate report", "selectYear": "Select Year for Emissions Calculation", "selectYearDescription": "Choose the year you want to calculate emissions for:", "selectYearLabel": "Year", "selectYearPlaceholder": "Select Year", "continue": "Continue", "cancel": "Cancel", "mainTitle": "GHG Emissions Calculator", "mainDescription": "Manage and calculate your organization's Greenhouse Gas Emissions", "sections": {"stationary": "Stationary Emissions", "mobile": "Mobile Emissions", "fugitive": "Fugitive Emissions", "agricultural": "Agricultural Emissions", "scope2Electricity": "Electricity", "scope2District": "District Energy"}, "sectionTitles": {"scope1": "Scope 1: Direct Emissions", "scope2": "Scope 2: Indirect Emissions", "scope1Stationary": "Scope 1: Stationary Combustion", "scope1Mobile": "Scope 1: Mobile Combustion", "scope1Fugitive": "Scope 1: Fugitive Emissions", "agriculture": "Agricultural Emissions (Biogenic Sources)", "scope2Electricity": "Scope 2: Purchased Electricity", "scope2District": "Scope 2: Heating, Cooling & Process Energy (Steam)"}, "sectionDescriptions": {"scope1Stationary": "Emissions resulting from the use of fuel for heating buildings or for other purposes (e.g., in equipment such as a generator). Please complete the data for all types of fuel consumed in stationary sources in the selected year. If no actual data is available, you may estimate emissions by floor area.", "scope1Mobile": "Emissions from company-owned vehicles. Choose actual fuel data or estimate via distance or expense.", "scope1Fugitive": "Fugitive emissions from refrigeration and air conditioning result from leakage and service over the operational life of the equipment and from disposal at the end of the useful life of the equipment. No estimation is allowed. Please enter the actual leaked or serviced amount (in kg).", "agriculture": "This section applies only if your company operates in the agricultural industry. It includes enteric fermentation, manure management, and fertiliser use.", "scope2Electricity": "Enter the amount of purchased or externally supplied electricity. If you do not have consumption data, you may enter the annual cost instead. For renewable energy, indicate if you have a contract/PPA or guarantees of origin (RES).", "scope2District": "Enter the amount of externally purchased or supplied heating, cooling, or steam. If you do not have consumption data, you may estimate by area. Only include district energy, not local sources."}, "oldSectionTitles": {"scope1": "Scope 1: Direct Emissions", "scope2": "Scope 2: Indirect Emissions"}, "forms": {"common": {"action": "Action", "addRow": "Add Row", "selectOption": "Select option", "total": "Total: {total} tCO₂e", "emissions": "Emissions (tCO₂e)", "delete": "Delete", "deleteRow": "Delete row", "totalEmissionsColumn": "Total emissions (tCO₂e)", "noRowsMessage": "No data rows added yet. Click 'Add Row' to get started.", "entericFermentation": "Enteric Fermentation", "fertiliserUse": "Fertiliser Use", "tco2e": "tCO₂e", "columnLabels": {"fuel": "Fuel", "consumption": "Consumption", "unit": "Unit", "area": "Estimation: Area", "areaUnit": "Area Unit", "category": "Category", "fuelType": "Fuel Type", "distance": "Distance", "distanceUnit": "Unit", "expense": "Expense (EUR)", "name": "Gas", "amount": "Amount", "animal": "Animal", "count": "Count", "fieldPercentage": "% of manure that goes into the field", "type": "Type", "cost": "Annual Cost (EUR)", "hasPPA": "PPA/Guarantee?", "annualConsumption": "Annual Consumption", "estimationByArea": "Estimation by Area"}, "options": {"fuels": {"naturalGas": "Natural gas", "heatingOil": "Heating oil", "diesel": "Diesel", "coalDomestic": "Coal (domestic)", "coalIndustrial": "Coal (industrial)", "lpg": "LPG", "propane": "Propane", "biomassWoodLogs": "Biomass - wood logs", "biomassWoodChips": "Biomass - wood chips", "biomassWoodPellets": "Biomass - wood pellets", "biomassGrassStraw": "Biomass - grass/straw"}, "vehicleCategories": {"passengerCars": "Passenger cars", "lcvs": "LCVs", "hgvs": "HGVs", "busesAndCoaches": "Buses and coaches", "other": "Other"}, "fuelTypes": {"petrol": "PETROL", "diesel": "DIESEL", "lpg": "LPG", "cng": "CNG"}, "gases": {"r134a": "R134a", "r404a": "R404A", "r410a": "R410A", "r32": "R32", "sf6": "SF6", "ch4": "CH4", "n2o": "N2O", "co2": "CO2"}, "animals": {"dairyCattle": "Dairy Cattle", "nonDairyCattle": "Non-dairy cattle", "sheep": "Sheep", "swine": "Swine", "goats": "Goats", "horses": "Horses", "poultry": "Poultry", "rabbit": "Rabbit"}, "fertiliserTypes": {"inorganicNitrogen": "Inorganic Nitrogen fertilizers", "organicNitrogen": "Organic Nitrogen fertilizers", "limestone": "Limestone", "dolomite": "Dolomite", "animalManure": "Animal manure purposefully applied to soils"}, "electricityTypes": {"nonRes": "non-RES", "res": "RES"}, "districtCategories": {"heating": "Heating", "cooling": "Cooling", "steam": "Steam"}, "yesNo": {"yes": "YES", "no": "NO"}}}, "stationary": {"description": "Enter fuel consumption data for stationary combustion sources (boilers, generators, etc.)", "annualConsumptionNote": "Enter annual consumption amounts for each fuel type used in your facilities.", "fuelsTitle": "Fossil Fuels", "biomassTitle": "Biomass Fuels"}, "mobile": {"description": "Enter data for mobile combustion sources (vehicles, equipment, etc.)", "methodNote": "Choose one calculation method per row: fuel consumption, distance traveled, or expense-based.", "totalEmissionsColumn": "Total emissions (tCO₂e)"}, "agricultural": {"description": "Enter data for agricultural emissions (livestock, fertilizers, etc.)", "animalsTitle": "Livestock", "fertilizersTitle": "Fertilizers", "fertilisersTitle": "Fertilizers", "companyQuestion": "Does your company operate in the agricultural industry?", "no": "No", "yes": "Yes", "totalEmissionsColumn": "Total emissions (tCO₂e)"}, "electricity": {"description": "Enter electricity consumption data", "annualConsumptionNote": "Please enter your annual consumption for each electricity type.", "totalEmissionsColumn": "Total emissions (tCO₂e)", "sectionTotal": "Section Total: {total} tCO₂e", "addRow": "Add Row"}, "district": {"description": "Enter district energy consumption data (heating, cooling, steam)", "annualConsumptionNote": "Please enter your annual consumption for each category.", "totalEmissionsColumn": "Total emissions (tCO₂e)"}, "fugitive": {"description": "Add your fugitive emissions below.", "annualConsumptionNote": "Please enter your annual consumption for each gas/type.", "addRow": "Add Fugitive Row", "source": "Source", "amount": "Amount", "unit": "Unit", "selectSource": "Select source...", "selectUnit": "Select unit...", "enterAmount": "Enter amount", "sectionTotal": "Section Total"}}, "report": {"title": "GHG Emissions Report", "description": "Greenhouse Gas Emissions report for {year}.", "editData": "Edit Data", "backToOverview": "Back to Overview", "noDataTitle": "No Report Data Available", "noDataDescription": "No emissions data found for year {year}. Please calculate emissions first.", "calculateEmissionsFor": "Calculate Emissions for {year}", "failedToLoad": "Failed to load report data for year {year}. Please try again.", "summaryOf": "Summary of", "scope1And2Emissions": "Scope 1 & 2 emissions", "totalEmissions": "Total emissions", "totalScope1Emissions": "Total Scope 1 emissions", "totalScope2Emissions": "Total Scope 2 emissions", "tco2ePerYear": "tCO₂e/year", "comparableToTitle": "Your total emissions in scope 1 and 2 are comparable to:", "treesToAbsorb": "trees to absorb generated emissions within 1 year", "kmsDriven": "kms driven (petrol)", "timesAroundGlobe": "times traveling around the globe by car", "breakdownTableTitle": "Breakdown Table: Scope 1 & 2 Emissions", "category": "Category", "emissions": "Emissions", "percentShare": "% Share", "scope1Direct": "Scope 1 (direct emissions)", "scope2Indirect": "Scope 2 (indirect emissions)", "stationaryCombustion": "Stationary Combustion", "mobileCombustion": "Mobile Combustion", "fugitiveEmissions": "Fugitive Emissions", "agricultural": "Agricultural", "biogenic": "Biogenic", "electricity": "Electricity", "districtEnergy": "District Energy", "purchasedElectricity": "Purchased Electricity", "heatingCoolingProcess": "Heating, Cooling & Process Energy (Steam)", "resCertificatePPA": "RES Certificate / PPA Available", "renewableEnergy": "Renewable Energy", "available": "Available", "notAvailable": "Not available", "emissionsNotIncluded": "Emissions not included in Scopes 1 and 2:", "emissionsFromBiomass": "Emissions from biomass (only CO₂)", "biomassNote": "CO₂ emissions from biomass combustion are out of scopes (1 and 2). CH₄ and N₂O are included in Scope 1 (stationary combustion).", "emissionsFromBiogenicSources": "Emissions from Biogenic Sources", "biogenicSourcesNote": "Emissions from agricultural activities (livestock, fertilizers) are biogenic and reported separately from Scopes 1 and 2.", "shareOfScope1And2": "Share of Scope 1 and 2 [%]", "breakdownOfScope1": "Breakdown of Scope 1 GHG emissions [tCO2e]", "breakdownOfScope2": "Breakdown of Scope 2 GHG emissions [tCO2e]", "stationaryEmissions": "Stationary emissions", "nonStationaryEmissions": "Non-stationary emissions", "biogenicEmissions": "Biogenic emissions", "districtEmissions": "District emissions", "reportTitle": "GHG Emissions Report - {year}", "reportDescription": "Greenhouse Gas Emissions report for {year}.", "loadingReport": "Loading report data...", "noReportData": "No report data available", "noDataFound": "No emissions data found for year {year}. Please calculate emissions first."}}}
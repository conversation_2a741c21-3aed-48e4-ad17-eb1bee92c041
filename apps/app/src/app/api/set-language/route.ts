/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { Language } from "@/utils/constants"
import type { NextRequest } from "next/server"
import { NextResponse } from "next/server"
import { LANGUAGE_COOKIE_KEY } from "@/utils/constants"

export async function POST(req: NextRequest) {
  const { locale } = (await req.json()) as { locale: Language }

  const res = NextResponse.json({ success: true })
  res.cookies.set(LANGUAGE_COOKIE_KEY, locale, {
    path: "/",
    maxAge: 60 * 60 * 24 * 365,
  })
  return res
}

/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

"use client"

import type { SectionKey } from "@/components/ghg-calculator/calculatorSections"
import type {
  AnimalRow,
  DistrictEnergyRow,
  ElectricityRow,
  FertiliserRow,
  FugitiveRow,
  MobileRow,
  StationaryRow,
} from "@/lib/ghg-calculator/types"
import type { GHGYearData } from "@impactly/integration/backend"
import { useEffect, useRef, useState } from "react"
import { scope1Sections, scope2Sections } from "@/components/ghg-calculator/calculatorSections"
import { useEmissions } from "@/components/ghg-calculator/EmissionsContext"
import SectionCard from "@/components/ghg-calculator/SectionCard"
import { api } from "@/lib/trpc-provider"
import { formatNumber } from "@/utils/formatNumber"
import { handleTRPCError } from "@/utils/trpc-error-handler"
import { useTranslations } from "next-intl"
import { useRouter } from "nextjs-toploader/app"

import { PathBreadcrumb } from "@kreios/admin-layout/automatic-breadcrumbs"
import { PageHeader } from "@kreios/admin-layout/components/page-header"
import { Button } from "@kreios/ui/button"
import { toast } from "@kreios/ui/sonner"

// Type guard for API response data
function isValidGHGData(data: unknown): data is GHGYearData {
  return typeof data === "object" && data !== null
}

interface YearCalculatorPageProps {
  params: {
    year: string
  }
}

export default function YearCalculatorPage({ params }: YearCalculatorPageProps) {
  const year = parseInt(params.year)
  const t = useTranslations("ghgCalculator")
  const { formData, setFormData, resetFormData, emissions } = useEmissions()
  const router = useRouter()
  const [sectionTotals, setSectionTotals] = useState<Record<SectionKey, number>>({
    stationary: 0,
    mobile: 0,
    fugitive: 0,
    agricultural: 0,
    scope2Electricity: 0,
    scope2District: 0,
  })

  // Track if we've already loaded data for this year to prevent infinite loops
  const hasLoadedDataRef = useRef<number | null>(null)

  // tRPC mutation for saving GHG data
  const utils = api.useUtils()
  const saveGHGDataMutation = api.companies.saveGHGData.useMutation({
    onSuccess: async () => {
      toast.success(t("saveSuccess"))
      // Invalidate both queries to refresh the listing page and current year data
      await utils.companies.getAllGHGYears.invalidate()
      await utils.companies.getGHGDataForYear.invalidate({ year: year })
    },
    onError: (error) => {
      console.error("Failed to save GHG data:", error)
      handleTRPCError(error, t)
    },
  })

  // tRPC query for loading GHG data using the new API endpoint
  const { data: ghgYearData, isLoading: isLoadingGHGData } = api.companies.getGHGDataForYear.useQuery(
    { year: year },
    {
      retry: false, // Don't retry if no data exists
      refetchOnWindowFocus: false, // Don't refetch when window gains focus
    }
  )

  const scope1Total = sectionTotals.stationary + sectionTotals.mobile + sectionTotals.fugitive
  const scope2Total = sectionTotals.scope2Electricity + sectionTotals.scope2District

  // Reset the loaded data ref and context when year changes
  useEffect(() => {
    hasLoadedDataRef.current = null
    // Reset context immediately when year changes to prevent showing stale data
    resetFormData()
  }, [year, resetFormData])

  // Load saved data for this specific year from API only

  useEffect(() => {
    if (isLoadingGHGData) return
    if (hasLoadedDataRef.current === year) return

    try {
      if (ghgYearData && isValidGHGData(ghgYearData)) {
        // Convert API response to form data format

        const agriculturalData = ghgYearData.agricultural
        const hasAgriculturalData = agriculturalData.animalRows.length > 0 || agriculturalData.fertiliserRows.length > 0

        const convertedFormData = {
          stationary: {
            fuelRows: ghgYearData.stationary.fuelRows as StationaryRow[],
            biomassRows: ghgYearData.stationary.biomassRows as StationaryRow[],
          },
          mobile: ghgYearData.mobile as MobileRow[],
          fugitive: ghgYearData.fugitive as FugitiveRow[],
          agricultural: {
            animalRows: agriculturalData.animalRows as AnimalRow[],
            fertiliserRows: agriculturalData.fertiliserRows as FertiliserRow[],
          },
          electricity: ghgYearData.electricity as ElectricityRow[],
          district: ghgYearData.district as DistrictEnergyRow[],
          calculatedTotals: ghgYearData.calculatedTotals,
          metadata: ghgYearData.metadata ?? {
            lastSaved: new Date().toISOString(),
            version: "1.0",
          },
          // Add agricultural industry flag
          hasAgriculturalIndustry: hasAgriculturalData,
        }

        // Use data from API
        setFormData(convertedFormData)
        console.log(`Loaded GHG Calculator data from API for year ${year}`, convertedFormData)
        hasLoadedDataRef.current = year
      } else {
        // No data from API - reset to default
        console.log(`No saved data for year ${year}, resetting to default`)
        resetFormData()
        hasLoadedDataRef.current = year
      }
    } catch (error) {
      console.error(`Failed to load saved data for year ${year}:`, error)
      resetFormData()
      hasLoadedDataRef.current = year
    }
  }, [year, ghgYearData, isLoadingGHGData, setFormData, resetFormData])

  const handleSave = async () => {
    try {
      // Debug: Log the current form data to see what's being captured
      console.log("=== DEBUG: Current formData before save ===")
      console.log("formData:", JSON.stringify(formData, null, 2))
      console.log("sectionTotals:", sectionTotals)
      console.log("emissions:", emissions)
      console.log("=== END DEBUG ===")

      const dataToSave = {
        ...formData,
        metadata: {
          ...formData.metadata,
          lastSaved: new Date().toISOString(),
          year: year,
        },
        // Save the calculated totals
        calculatedTotals: {
          sectionTotals: {
            ...sectionTotals,
            biomass: emissions.biomass ?? 0,
          },
          scope1Total: scope1Total,
          scope2Total: scope2Total,
          scope1AndScope2TotalEmissions: scope1Total + scope2Total,
        },
      }

      // Use tRPC mutation to save data
      await saveGHGDataMutation.mutateAsync({
        year: year,
        data: dataToSave,
      })
    } catch (error) {
      console.error("Failed to save GHG data:", error)
      // Error handling is done in the mutation's onError callback
    }
  }

  const handleReset = () => {
    if (confirm(t("resetConfirm"))) {
      resetFormData()
      toast.success(t("resetSuccess"))
    }
  }

  const handleTotalChange = (section: SectionKey, total: number) => {
    setSectionTotals((prev) => ({ ...prev, [section]: total }))
  }

  // Show loading state while fetching data
  if (isLoadingGHGData) {
    return (
      <>
        <PathBreadcrumb id={params.year}>{year}</PathBreadcrumb>
        <PageHeader
          title={
            <div>
              <h1 className="mb-2 text-3xl font-bold text-foreground">
                {t("title")} - {year}
              </h1>
              <p className="text-muted-foreground">{t("description", { year })}</p>
            </div>
          }
          includeInBreadcrumb={false}
        />
        <div className="flex items-center justify-center p-8">
          <div className="text-center">
            <div className="mb-4 h-8 w-8 animate-spin rounded-full border-4 border-gray-300 border-t-blue-600"></div>
            <p className="text-muted-foreground">Loading GHG Calculator data...</p>
          </div>
        </div>
      </>
    )
  }

  return (
    <>
      <PathBreadcrumb id={params.year}>{year}</PathBreadcrumb>
      <PageHeader
        title={
          <div>
            <h1 className="mb-2 text-3xl font-bold text-foreground">
              {t("title")} - {year}
            </h1>
            <p className="text-muted-foreground">{t("description", { year })}</p>
          </div>
        }
        includeInBreadcrumb={false}
      />
      <div className="sticky top-20 z-20 ml-auto mt-4 flex gap-3 px-2 sm:px-4 md:px-8">
        <Button type="button" onClick={handleSave} disabled={saveGHGDataMutation.isPending}>
          {saveGHGDataMutation.isPending ? t("saving") : t("save")}
        </Button>
        <Button variant={"destructive"} onClick={handleReset}>
          {t("resetAll")}
        </Button>
        <Button
          onClick={() => {
            const totalEmissions = scope1Total + scope2Total
            if (totalEmissions > 0) {
              router.push(`/admin/ghg-calculator/${year}/report`)
            }
          }}
          disabled={scope1Total + scope2Total === 0}
          className={`inline-flex items-center gap-2 rounded px-6 py-2 text-white focus:outline-none ${
            scope1Total + scope2Total > 0
              ? "bg-gray-600 hover:bg-gray-700 dark:bg-gray-700 dark:hover:bg-gray-600"
              : "cursor-not-allowed bg-gray-400 dark:bg-gray-600"
          }`}
          title={scope1Total + scope2Total === 0 ? t("noDataForReport") : undefined}
        >
          {t("generateReport")}
        </Button>
      </div>
      <div className="mx-2 mt-6 sm:mx-6 md:mx-8">
        {/* Summary Cards */}
        <div className="mb-8 grid w-full grid-cols-1 gap-4 md:grid-cols-3">
          <div className="rounded-lg border border-blue-600 bg-blue-50 p-6 dark:border-blue-400 dark:bg-gray-800">
            <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-300">{t("scope1Emissions")}</h3>
            <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">{formatNumber(scope1Total)} tCO₂e</p>
            <p className="text-sm text-blue-700 dark:text-blue-500">{t("scope1Description")}</p>
          </div>
          <div className="rounded-lg border border-green-600 bg-green-50 p-6 dark:border-green-400 dark:bg-gray-800">
            <h3 className="text-lg font-semibold text-green-900 dark:text-green-300">{t("scope2Emissions")}</h3>
            <p className="text-2xl font-bold text-green-600 dark:text-green-400">{formatNumber(scope2Total)} tCO₂e</p>
            <p className="text-sm text-green-700 dark:text-green-500">{t("scope2Description")}</p>
          </div>
          <div className="rounded-lg border border-purple-600 bg-purple-50 p-6 dark:border-purple-400 dark:bg-gray-800">
            <h3 className="text-lg font-semibold text-purple-900 dark:text-purple-300">{t("totalEmissions")}</h3>
            <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
              {formatNumber(scope1Total + scope2Total)} tCO₂e
            </p>
            <p className="text-sm text-purple-700 dark:text-purple-500">{t("totalDescription")}</p>
          </div>
        </div>

        {/* Scope 1 Sections */}
        <div className="mb-8 w-full">
          <h2 className="mb-4 text-2xl font-semibold text-foreground">{t("sectionTitles.scope1")}</h2>
          <div className="w-full space-y-6">
            {scope1Sections.map((section) => (
              <SectionCard
                key={section.key}
                icon={section.icon}
                title={t(`sections.${section.key}`)}
                total={sectionTotals[section.key]}
              >
                <section.Form onTotalChange={(total) => handleTotalChange(section.key, total)} />
              </SectionCard>
            ))}
          </div>
        </div>

        {/* Scope 2 Sections */}
        <div className="mb-8 w-full">
          <h2 className="mb-4 text-2xl font-semibold text-foreground">{t("sectionTitles.scope2")}</h2>
          <div className="w-full space-y-6">
            {scope2Sections.map((section) => (
              <SectionCard
                key={section.key}
                icon={section.icon}
                title={t(`sections.${section.key}`)}
                total={sectionTotals[section.key]}
              >
                <section.Form onTotalChange={(total) => handleTotalChange(section.key, total)} />
              </SectionCard>
            ))}
          </div>
        </div>
      </div>
    </>
  )
}

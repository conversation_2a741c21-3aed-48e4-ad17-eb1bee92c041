"use client"

/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { RouterOutput } from "@/lib/trpc-provider"
import type { ColumnDef } from "@tanstack/react-table"
import { useMemo, useState } from "react"
import { api } from "@/lib/trpc-provider"
import { formatNumber } from "@/utils/formatNumber"
import { Edit, FileText } from "lucide-react"
import { useTranslations } from "next-intl"
import { useRouter } from "nextjs-toploader/app"

import { PageHeader } from "@kreios/admin-layout/components/page-header"
import { DataTableInline } from "@kreios/datatable/data-table-inline"
import { createEnhancedColumnHelper } from "@kreios/datatable/enhanced-column-helper"
import { But<PERSON> } from "@kreios/ui/button"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@kreios/ui/dialog"
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from "@kreios/ui/select"
import { Skeleton } from "@kreios/ui/skeleton"

type YearlyEmissionData = RouterOutput["companies"]["getAllGHGYears"][number]

const columnHelper = createEnhancedColumnHelper<YearlyEmissionData>()

export default function GHGCalculatorMainPage() {
  const t = useTranslations("ghgCalculator")
  const router = useRouter()
  const [showYearModal, setShowYearModal] = useState(false)
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear() - 1)

  // tRPC query to get all GHG years data
  const {
    data: yearlyData = [],
    isLoading,
    error,
  } = api.companies.getAllGHGYears.useQuery(undefined, {
    retry: 2,
    refetchOnWindowFocus: false,
  })

  // Debug logging
  console.log("GHG Years Query State:", {
    isLoading,
    hasData: !!yearlyData.length,
    data: yearlyData,
    error,
  })

  // Define columns for the DataTable
  const columns = useMemo(
    () =>
      [
        columnHelper.custom("year", {
          title: t("year"),
          cell: (ctx) => <span className="font-medium">{ctx.getValue()}</span>,
        }),
        columnHelper.custom("scope1Total", {
          title: t("scope1EmissionsTable"),
          cell: (ctx) => `${formatNumber(Number(ctx.getValue()) || 0)} tCO₂e`,
        }),
        columnHelper.custom("scope2Total", {
          title: t("scope2EmissionsTable"),
          cell: (ctx) => `${formatNumber(Number(ctx.getValue()) || 0)} tCO₂e`,
        }),
        columnHelper.custom("scope1and2TotalEmissions", {
          title: t("totalEmissionsTable"),
          cell: (ctx) => <span className="font-medium">{formatNumber(Number(ctx.getValue()) || 0)} tCO₂e</span>,
        }),
        columnHelper.custom("year", {
          id: "actions",
          title: t("actions"),
          cell: (ctx) => {
            const year = ctx.getValue()
            const rowData = ctx.row.original
            const hasData = Number(rowData.scope1and2TotalEmissions) > 0

            return (
              <div className="flex space-x-2">
                <button
                  onClick={() => router.push(`/admin/ghg-calculator/${year}`)}
                  className="text-primary hover:text-primary/80"
                  title={t("edit")}
                >
                  <Edit className="h-4 w-4" />
                </button>
                <button
                  onClick={() => hasData && router.push(`/admin/ghg-calculator/${year}/report`)}
                  className={`${
                    hasData
                      ? "text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                      : "cursor-not-allowed text-gray-400 dark:text-gray-600"
                  }`}
                  title={hasData ? t("viewReport") : t("noDataForReport")}
                  disabled={!hasData}
                >
                  <FileText className="h-4 w-4" />
                </button>
              </div>
            )
          },
        }),
      ] as ColumnDef<YearlyEmissionData, unknown>[],
    [t, router]
  )

  const handleCalculateEmissions = () => {
    setShowYearModal(true)
  }

  const handleYearSelect = () => {
    setShowYearModal(false)
    // Navigate to year-specific calculator
    router.push(`/admin/ghg-calculator/${selectedYear}`)
  }

  const generateYearOptions = () => {
    const currentYear = new Date().getFullYear()
    const years = []
    for (let year = currentYear; year >= currentYear - 4; year--) {
      years.push(year)
    }
    return years
  }

  // Skeleton table component
  const SkeletonTable = () => (
    <div className="rounded-lg border">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-border">
          <thead className="bg-muted">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-muted-foreground">
                {t("year")}
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-muted-foreground">
                {t("scope1EmissionsTable")}
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-muted-foreground">
                {t("scope2EmissionsTable")}
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-muted-foreground">
                {t("totalEmissionsTable")}
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-muted-foreground">
                {t("actions")}
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-border bg-background">
            {Array.from({ length: 5 }).map((_, index) => (
              <tr key={index} className="hover:bg-muted/50">
                <td className="whitespace-nowrap px-6 py-4">
                  <Skeleton className="h-4 w-16" />
                </td>
                <td className="whitespace-nowrap px-6 py-4">
                  <Skeleton className="h-4 w-24" />
                </td>
                <td className="whitespace-nowrap px-6 py-4">
                  <Skeleton className="h-4 w-24" />
                </td>
                <td className="whitespace-nowrap px-6 py-4">
                  <Skeleton className="h-4 w-28" />
                </td>
                <td className="space-x-2 whitespace-nowrap px-6 py-4">
                  <Skeleton className="inline-block h-4 w-4" />
                  <Skeleton className="inline-block h-4 w-4" />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )

  return (
    <div>
      <PageHeader
        title={
          <div>
            <h1 className="mb-2 text-3xl font-bold">{t("mainTitle")}</h1>
            <p className="text-muted-foreground">{t("mainDescription")}</p>
          </div>
        }
        includeInBreadcrumb={false}
      >
        <Button onClick={handleCalculateEmissions}>
          <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          {t("calculateEmissions")}
        </Button>
      </PageHeader>

      <div className="p-4 md:p-6">
        {isLoading ? (
          <SkeletonTable />
        ) : error ? (
          <div className="flex items-center justify-center p-8 text-red-600">
            <p>Error loading emissions data: {error.message}</p>
          </div>
        ) : yearlyData.length === 0 ? (
          <div className="rounded-lg border">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-border">
                <thead className="bg-muted">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-muted-foreground">
                      {t("year")}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-muted-foreground">
                      {t("scope1EmissionsTable")}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-muted-foreground">
                      {t("scope2EmissionsTable")}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-muted-foreground">
                      {t("totalEmissionsTable")}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-muted-foreground">
                      {t("actions")}
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-border bg-background">
                  <tr>
                    <td colSpan={5} className="px-6 py-8 text-center text-muted-foreground">
                      {t("noEmissionsData")}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        ) : (
          <div className="mt-6">
            <DataTableInline data={yearlyData} columns={columns} rowIdKey="year" toolbar={false} />
          </div>
        )}
      </div>

      {/* Year Selection Modal */}
      <Dialog open={showYearModal} onOpenChange={setShowYearModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>{t("selectYear")}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">{t("year")}</label>
              <Select value={selectedYear.toString()} onValueChange={(value) => setSelectedYear(parseInt(value))}>
                <SelectTrigger className="w-full">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {generateYearOptions().map((year) => (
                      <SelectItem key={year} value={year.toString()}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setShowYearModal(false)}>
                {t("cancel")}
              </Button>
              <Button onClick={handleYearSelect}>{t("continue")}</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}

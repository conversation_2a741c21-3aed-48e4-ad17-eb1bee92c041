/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

// import Link from "next/link"
import type { ReactNode } from "react"
import { EmissionsProvider } from "@/components/ghg-calculator/EmissionsContext"

export default function GHGCalculatorLayout({ children }: { children: ReactNode }) {
  return <EmissionsProvider>{children}</EmissionsProvider>
}

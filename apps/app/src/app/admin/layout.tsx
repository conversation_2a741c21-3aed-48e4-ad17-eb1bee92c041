/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import type { PortalUserDocument } from "@impactly/domain/portal-users/elastic"
import type { ComponentProps, FC, ReactNode } from "react"
import React from "react"
import { cookies } from "next/headers"
import { redirect } from "next/navigation"
import ConsentPopup from "@/components/cookie-consent"
import { FeatureFlagsProvider } from "@/components/feature-flag-provider"
import { HeaderActions } from "@/components/header/header-actions"
import { LanguageSelector } from "@/components/language-selector"
import { EnhancedAdminLayout as AdminLayout } from "@/components/layout/enhanced-admin-layout"
import { LogoLarge, LogoSmall } from "@/components/logo"
import { ImpactlySearchCommands } from "@/components/search/search"
import { SidebarMenuLink } from "@/components/sidebar/sidebar-menu-link"
import { TrackedUserMenu } from "@/components/sidebar/sidebar-user-menu"
import { TRPCReactProvider } from "@/lib/trpc-provider"
import { getPortalUserByEmail } from "@impactly/domain/portal-users/utils/get-portal-user-by-email"
import { getPortalUserRoles } from "@impactly/domain/portal-users/utils/get-portal-user-roles"
import { enableCopilot, roles } from "@impactly/flags"
import { Building2Icon, Calculator, FileQuestionIcon } from "lucide-react"
import { NextIntlClientProvider } from "next-intl"
import { getTranslations } from "next-intl/server"
import NextTopLoader from "nextjs-toploader"

import type { SidebarUserMenu } from "@kreios/admin-layout/sidebar/sidebar-user-menu"
import { AutomaticBreadcrumb } from "@kreios/admin-layout/automatic-breadcrumbs"
import { SentryFeedbackWidget } from "@kreios/admin-layout/components/sentry-user-feedback-widget"
import { ThemeProvider } from "@kreios/admin-layout/components/theme-provider"
import { SidebarAppHome } from "@kreios/admin-layout/sidebar/sidebar-app-home"
import { auth } from "@kreios/auth"
import { AdminCopilotDrawer } from "@kreios/copilot"
import {
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
} from "@kreios/ui/sidebar"
import { Toaster } from "@kreios/ui/sonner"
import { TooltipProvider } from "@kreios/ui/tooltip"
import { promiseObjectAll } from "@kreios/utils/promise-object-all"

const Sidebar: FC<{ user: ComponentProps<typeof SidebarUserMenu>["user"]; portalUser: PortalUserDocument }> = async ({
  user,
  portalUser,
}) => {
  const { isAdmin, isClientAdmin, isCompanyUser, isObserver } = getPortalUserRoles(portalUser)
  const companyUser = isCompanyUser || isObserver
  const t = await getTranslations("sidebar")

  return (
    <>
      <SidebarHeader>
        <SidebarAppHome icon={<LogoSmall className="size-4" />} title={t("title")} subtitle={t("subtitle")} />
      </SidebarHeader>
      {/* <SidebarGroup>
        <SidebarMenu>
          {(isAdmin || isClientAdmin) && (
            <SidebarMenuItem>
              <SidebarMenuLink label={t("navigation.Dashboard")} href="/admin" icon={<Home />} />
            </SidebarMenuItem>
          )}
          <SidebarGlobalSearchItem />
          <SidebarNotificationItem disabled />
        </SidebarMenu>
      </SidebarGroup> */}

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>{t("navigation.title")}</SidebarGroupLabel>
          <SidebarMenu>
            {/* Portfolios - Admin only */}
            {/* {(isAdmin || isDataViewer) && (
              <SidebarMenuItem>
                <SidebarMenuLink
                  match="/admin/portfolios/:id"
                  href="/admin/portfolios"
                  icon={<GalleryHorizontalIcon />}
                  label={t("navigation.Portfolios")}
                />
              </SidebarMenuItem>
            )} */}
            {/* Companies - Admin, Client Admin, and Company User (scoped) */}
            {(isAdmin || isClientAdmin || companyUser) && (
              <SidebarMenuItem>
                <SidebarMenuLink
                  match="/admin/companies/:id"
                  href={companyUser ? `/admin/companies/${portalUser.companyId}` : "/admin/companies"}
                  icon={<Building2Icon />}
                  label={companyUser ? t("navigation.MyCompany") : t("navigation.Companies")}
                />
              </SidebarMenuItem>
            )}
            {/* Surveys - Admin, Client Admin, and Company User */}
            {(isAdmin || isClientAdmin || companyUser) && (
              <React.Fragment>
                {/* <SidebarMenuItem>
                  <SidebarMenuLink
                    match={companyUser ? "" : "/admin/surveys/:id"}
                    href={companyUser ? "/admin/company-surveys" : "/admin/surveys"}
                    icon={<FileQuestionIcon />}
                    label={companyUser ? t("navigation.Surveys") : t("navigation.SurveyBatches")}
                  />
                </SidebarMenuItem> */}
                <SidebarMenuItem>
                  <SidebarMenuLink
                    match={companyUser ? "" : "/admin/assessment-batches/:id"}
                    href={companyUser ? "/admin/self-assessments" : "/admin/assessment-batches"}
                    icon={<FileQuestionIcon />}
                    label={companyUser ? t("navigation.selfAssessments") : t("navigation.AssessmentBatches")}
                  />
                </SidebarMenuItem>
                {companyUser && (
                  <SidebarMenuItem>
                    <SidebarMenuLink
                      match="/admin/ghg-calculator"
                      href={"/admin/ghg-calculator"}
                      icon={<Calculator />}
                      label={t("navigation.GHGCalculator")}
                    />
                  </SidebarMenuItem>
                )}
              </React.Fragment>
            )}
            {/* <SidebarMenuItem>
              <SidebarMenuLink
                match="/admin/self-assesment"
                href={"/admin/self-assesment/1"}
                icon={<FileQuestionIcon />}
                label={t("navigation.selfAssessment")}
              />
            </SidebarMenuItem> */}
            {/* Evaluation Requests - Admin only */}
            {/* {isAdmin && (
              <SidebarMenuItem>
                <SidebarMenuLink
                  match="/admin/evaluation-requests/:id"
                  href="/admin/evaluation-requests"
                  icon={<ThumbsUp />}
                  label={t("navigation.EvaluationRequests")}
                />
              </SidebarMenuItem>
            )} */}
          </SidebarMenu>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter>
        <TrackedUserMenu user={user} />
      </SidebarFooter>
    </>
  )
}

export default async function RootLayout({
  children,
  modal,
}: Readonly<{
  children: ReactNode
  modal: ReactNode
}>) {
  const session = await auth()

  if (!session?.user.email) redirect("/auth/login?callbackUrl=/admin")

  const portalUser = await getPortalUserByEmail(session.user.email)

  if (!portalUser) redirect("/unauthorized")

  const cookieString = cookies().toString()

  const { isAdmin, isClientAdmin } = getPortalUserRoles(portalUser)

  return (
    <FeatureFlagsProvider flags={await promiseObjectAll({ enableCopilot: enableCopilot(), roles: roles() })}>
      <ThemeProvider attribute="class" defaultTheme="light" disableTransitionOnChange>
        <Toaster />
        <SentryFeedbackWidget enabled={true} />
        <TooltipProvider>
          <TRPCReactProvider cookieString={cookieString}>
            <NextTopLoader />
            <AdminCopilotDrawer modal={modal}>
              <AdminLayout
                sidebar={<Sidebar portalUser={portalUser} user={session.user} />}
                globalSearch={isAdmin || isClientAdmin ? <ImpactlySearchCommands /> : undefined}
                header={
                  <>
                    <AutomaticBreadcrumb start={<LogoLarge className="h-[1.875rem] w-auto text-primary" />} />
                    <div className="ml-auto flex items-center gap-2">
                      <HeaderActions user={session.user} portalUser={portalUser} />
                      <LanguageSelector />
                    </div>
                  </>
                }
              >
                {children}
              </AdminLayout>
              <NextIntlClientProvider>
                <ConsentPopup />
              </NextIntlClientProvider>
            </AdminCopilotDrawer>
          </TRPCReactProvider>
        </TooltipProvider>
      </ThemeProvider>
    </FeatureFlagsProvider>
  )
}

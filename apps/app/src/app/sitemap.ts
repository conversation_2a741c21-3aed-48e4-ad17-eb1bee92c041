/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import type { MetadataRoute } from "next"

export default function sitemap(): MetadataRoute.Sitemap {
  return [
    { url: "https://portal.impactly.eu", priority: 1 },
    { url: "https://portal.impactly.eu/auth/login", priority: 0.8 },
    { url: "https://portal.impactly.eu/auth/verify-request", priority: 0.5 },
    { url: "https://portal.impactly.eu/auth/error", priority: 0.5 },
    { url: "https://portal.impactly.eu/coming-soon", priority: 0.5 },
    { url: "https://portal.impactly.eu/unauthorized", priority: 0.4 },
    { url: "https://portal.impactly.eu/terms-of-service", priority: 0.6 },
    { url: "https://portal.impactly.eu/privacy-policy", priority: 0.6 },
    { url: "https://portal.impactly.eu/cookie-policy", priority: 0.6 },
    { url: "https://portal.impactly.eu/request-access", priority: 0.7 },
    { url: "https://portal.impactly.eu/consent", priority: 0.7 },
  ]
}

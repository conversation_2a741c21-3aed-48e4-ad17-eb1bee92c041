/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import type { ReactNode } from "react"
import AgriculturalEmissionsForm from "@/components/ghg-calculator/AgriculturalEmissionsForm"
import DistrictEnergyForm from "@/components/ghg-calculator/DistrictEnergyForm"
import ElectricityForm from "@/components/ghg-calculator/ElectricityForm"
import FugitiveEmissionsForm from "@/components/ghg-calculator/FugitiveEmissionsForm"
import MobileEmissionsForm from "@/components/ghg-calculator/MobileEmissionsForm"
import StationaryEmissionsForm from "@/components/ghg-calculator/StationaryEmissionsForm"
import { Car, Factory, Leaf, Snowflake } from "lucide-react"

export type SectionKey = "stationary" | "mobile" | "fugitive" | "agricultural" | "scope2Electricity" | "scope2District"

export const scope1Sections: {
  key: SectionKey
  icon: ReactNode
  title: string
  Form: React.ComponentType<{ onTotalChange: (total: number) => void }>
}[] = [
  {
    key: "stationary",
    icon: <Factory className="h-6 w-6 text-primary" />,
    title: "Stationary Emissions",
    Form: StationaryEmissionsForm,
  },
  {
    key: "mobile",
    icon: <Car className="h-6 w-6 text-primary" />,
    title: "Mobile Emissions",
    Form: MobileEmissionsForm,
  },
  {
    key: "fugitive",
    icon: <Snowflake className="h-6 w-6 text-primary" />,
    title: "Fugitive Emissions",
    Form: FugitiveEmissionsForm,
  },
  {
    key: "agricultural",
    icon: <Leaf className="h-6 w-6 text-primary" />,
    title: "Agricultural",
    Form: AgriculturalEmissionsForm,
  },
]

export const scope2Sections: {
  key: SectionKey
  icon: ReactNode
  title: string
  Form: React.ComponentType<{ onTotalChange: (total: number) => void }>
}[] = [
  {
    key: "scope2Electricity",
    icon: <Factory className="h-6 w-6 text-primary" />,
    title: "Purchased Electricity",
    Form: ElectricityForm,
  },
  {
    key: "scope2District",
    icon: <Snowflake className="h-6 w-6 text-primary" />,
    title: "Heating, Cooling & Process Energy (Steam)",
    Form: DistrictEnergyForm,
  },
]

/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import React from "react"
import { formatNumber } from "@/utils/formatNumber"
import { useTranslations } from "next-intl"

import type { EmissionsSummary } from "./ReportSummary"

function percentShare(value: number, total: number) {
  if (!total) return "0.0%"
  return ((value / total) * 100).toFixed(1) + "%"
}

export const ReportBreakdownTable: React.FC<{
  emissions: EmissionsSummary
  resPpaAvailable?: boolean
}> = ({ emissions, resPpaAvailable }) => {
  const t = useTranslations("ghgCalculator.report")

  // Scope 1 breakdown
  const scope1Rows = [
    { label: t("stationaryCombustion"), value: emissions.scope1.stationary },
    { label: t("mobileCombustion"), value: emissions.scope1.mobile },
    { label: t("fugitiveEmissions"), value: emissions.scope1.fugitive },
  ]
  const scope1Total = emissions.scope1.total

  // Scope 2 breakdown
  const scope2Rows = [
    { label: t("purchasedElectricity"), value: emissions.scope2.electricity },
    { label: t("heatingCoolingProcess"), value: emissions.scope2.district },
    { label: t("resCertificatePPA"), value: resPpaAvailable ? 1 : 0, isRes: true },
  ]
  const scope2Total = emissions.scope2.total

  return (
    <div className="flex-2">
      <h3 className="mb-4 text-[20px] font-semibold text-foreground">{t("breakdownTableTitle")}</h3>
      <table className="w-full border-collapse overflow-hidden rounded-lg bg-background text-[15px] shadow-md">
        <thead className="bg-muted">
          <tr>
            <th className="border-b border-border px-3 py-2 text-left text-foreground">{t("category")}</th>
            <th className="border-b border-border px-3 py-2 text-right text-foreground">
              {t("emissions")}
              <br />({t("tco2ePerYear")})
            </th>
            <th className="border-b border-border px-3 py-2 text-right text-foreground">{t("percentShare")}</th>
            <th className="border-b border-border px-3 py-2 text-center text-foreground">RES Certificate</th>
          </tr>
        </thead>
        <tbody>
          {/* Scope 1 */}
          <tr className="bg-muted/50">
            <td colSpan={4} className="px-3 py-2 font-semibold text-foreground">
              {t("scope1Direct")} —{" "}
              <span className="font-normal">
                {formatNumber(scope1Total)} {t("tco2ePerYear")}
              </span>
            </td>
          </tr>
          {scope1Rows.map((row) => (
            <tr key={row.label}>
              <td className="px-3 py-2 text-foreground">{row.label}</td>
              <td className="px-3 py-2 text-right text-foreground">{formatNumber(row.value)}</td>
              <td className="px-3 py-2 text-right text-foreground">{percentShare(row.value, scope1Total)}</td>
              <td className="px-3 py-2 text-center text-muted-foreground">—</td>
            </tr>
          ))}
          {/* Scope 2 */}
          <tr className="bg-muted/50">
            <td colSpan={4} className="px-3 py-2 font-semibold text-foreground">
              {t("scope2Indirect")} —{" "}
              <span className="font-normal">
                {formatNumber(scope2Total)} {t("tco2ePerYear")}
              </span>
            </td>
          </tr>
          {scope2Rows.map((row) =>
            row.isRes ? (
              <tr key={row.label}>
                <td className="px-3 py-2 text-foreground">{row.label}</td>
                <td className="px-3 py-2 text-center text-muted-foreground"></td>
                <td className="px-3 py-2 text-center text-muted-foreground"></td>
                <td className="px-3 py-2 text-center">
                  {resPpaAvailable ? (
                    <span className="font-semibold text-green-600 dark:text-green-400">{t("available")}</span>
                  ) : (
                    <span className="font-semibold text-red-700 dark:text-red-400">{t("notAvailable")}</span>
                  )}
                </td>
              </tr>
            ) : (
              <tr key={row.label}>
                <td className="px-3 py-2 text-foreground">{row.label}</td>
                <td className="px-3 py-2 text-right text-foreground">{formatNumber(row.value)}</td>
                <td className="px-3 py-2 text-right text-foreground">{percentShare(row.value, scope2Total)}</td>
                <td className="px-3 py-2 text-right text-muted-foreground">—</td>
              </tr>
            )
          )}
        </tbody>
      </table>
    </div>
  )
}

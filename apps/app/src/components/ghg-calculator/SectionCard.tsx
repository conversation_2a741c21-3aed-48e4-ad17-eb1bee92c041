/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import type { ReactNode } from "react"

interface SectionCardProps {
  icon: ReactNode
  title: string
  children: ReactNode
  total: number
}

const SectionCard = ({ icon, title, children }: SectionCardProps) => (
  <div className="mb-8 w-full rounded-xl border bg-card p-6 shadow">
    <div className="mb-4 flex items-center gap-2">
      {icon}
      <h2 className="text-xl font-bold text-foreground">{title}</h2>
    </div>
    <div>{children}</div>
  </div>
)

export default SectionCard

/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import React from "react"
import { useTranslations } from "next-intl"

export const ReportHeader: React.FC<{ year?: string; company?: string }> = () => {
  const t = useTranslations("ghgCalculator.report")

  return (
    <div className="mb-4 overflow-hidden rounded-sm border border-border">
      <div className="h-2 w-full bg-blue-800 dark:bg-blue-600" />
      <div className="flex items-center justify-between bg-background px-4 py-2 text-lg font-medium text-foreground">
        <span>{t("summaryOf")}</span>
        {/* <span className="flex-1 text-center font-bold">scope 1 &amp; 2 emissions in year {year}</span> */}
        {/* <span>{company}</span> */}
        <span className="flex-1 text-center font-bold">{t("scope1And2Emissions")}</span>
      </div>
    </div>
  )
}

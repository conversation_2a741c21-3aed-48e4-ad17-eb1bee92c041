/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import React from "react"
import { formatNumber } from "@/utils/formatNumber"
import { useTranslations } from "next-intl"

type CardProps = {
  label: string
  value: string | number
}

const SummaryCard: React.FC<CardProps> = ({ label, value }) => {
  const t = useTranslations("ghgCalculator.report")

  return (
    <div className="flex w-1/3 flex-col items-center">
      <div className="mb-1 text-sm font-semibold text-foreground">{label}</div>
      <div className="flex w-full flex-col items-center rounded border border-green-700 bg-green-100 p-2 text-green-700 dark:border-green-300 dark:bg-green-900 dark:text-green-300">
        <span className="text-2xl font-bold text-green-900 dark:text-green-100">{formatNumber(Number(value))}</span>
        <span className="text-xs text-green-700 dark:text-green-300">{t("tco2ePerYear")}</span>
      </div>
    </div>
  )
}

export const ReportSummaryCards: React.FC<{
  total: number | string
  scope1: number | string
  scope2: number | string
}> = ({ total, scope1, scope2 }) => {
  const t = useTranslations("ghgCalculator.report")

  return (
    <div className="mb-6 flex w-full justify-between gap-2">
      <SummaryCard label={t("totalEmissions")} value={total} />
      <SummaryCard label={t("totalScope1Emissions")} value={scope1} />
      <SummaryCard label={t("totalScope2Emissions")} value={scope2} />
    </div>
  )
}

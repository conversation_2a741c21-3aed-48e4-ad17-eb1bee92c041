/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
"use client"

import { useEffect, useState } from "react"
import { useEmissions } from "@/components/ghg-calculator/EmissionsContext"
import { scope2ElectricityEmissionFactors, toKWh } from "@/lib/ghg-calculator/calc-logic"
import { formSchema } from "@/lib/ghg-calculator/formSchema"
import { formatNumber } from "@/utils/formatNumber"
import { useTranslations } from "next-intl"

import { Button } from "@kreios/ui/button"

import type { Column } from "./DynamicTable"
import { DynamicTable } from "./DynamicTable"

const schema = formSchema.sections.find((s) => s.id === "scope2_electricity")
if (!schema) throw new Error("Missing schema for scope2_electricity")
const columns = schema.columns
const selectOptionsMap = Object.fromEntries(
  columns.filter((col) => col.type === "select").map((col) => [col.id, col.options as string[]])
)

type ElectricityRow = {
  type: string
  consumption: string
  unit: string
  cost: string
  hasPPA: string
}

// Real emission factors (DEFRA/IEA)
const emissionFactorNonRES = scope2ElectricityEmissionFactors.nonRES
const expenseFactorEUR = scope2ElectricityEmissionFactors.expensePerEUR

export default function ElectricityForm({ onTotalChange }: { onTotalChange?: (total: number) => void }) {
  const t = useTranslations("ghgCalculator.forms.electricity")
  const tSchema = useTranslations("ghgCalculator")
  const { setEmissions, formData, setFormData } = useEmissions()
  // Initialize from context
  const [rows, setRows] = useState<ElectricityRow[]>(formData.electricity)

  // Sync local state with context when formData changes
  useEffect(() => {
    setRows(formData.electricity)
  }, [formData.electricity])

  const getDefaultRow = (): ElectricityRow => {
    return {
      type: selectOptionsMap.type[0] || "",
      consumption: "",
      unit: selectOptionsMap.unit[0] || "",
      cost: "",
      hasPPA: selectOptionsMap.hasPPA[0] || "",
    }
  }

  const addRow = () => {
    const newRows = [...rows, getDefaultRow()]
    setRows(newRows)
    // Update context
    setFormData((prev) => ({
      ...prev,
      electricity: newRows,
    }))
  }

  const deleteRow = (index: number) => {
    const newRows = rows.filter((_, i) => i !== index)
    setRows(newRows)
    // Update context
    setFormData((prev) => ({
      ...prev,
      electricity: newRows,
    }))
  }

  const updateRow = (i: number, key: keyof ElectricityRow, value: string) => {
    const updated = [...rows]
    updated[i][key] = value
    // If type changes, update hasPPA accordingly
    if (key === "type") {
      // If type is RES, default hasPPA to NO, else blank
      updated[i].hasPPA = value === "RES" ? selectOptionsMap.hasPPA[0] || "" : ""
    }
    // Mutually exclusive: if entering consumption, clear cost; if entering cost, clear consumption
    if (key === "consumption") {
      updated[i].cost = ""
    }
    if (key === "cost") {
      updated[i].consumption = ""
    }
    setRows(updated)
    // Update context
    setFormData((prev) => ({
      ...prev,
      electricity: updated,
    }))
  }

  const calculateEmissions = (row: ElectricityRow) => {
    // If no consumption, fallback to cost
    if (!row.consumption && row.cost) {
      const cost = parseFloat(row.cost)
      if (!isNaN(cost) && cost > 0) {
        return +((cost * expenseFactorEUR) / 1000).toFixed(3) // tCO2e
      }
      return 0
    }
    const val = parseFloat(row.consumption)
    if (!isNaN(val) && val > 0) {
      const kWh = toKWh(val, row.unit)
      if (row.type === "non-RES") {
        return +((kWh * emissionFactorNonRES) / 1000).toFixed(3) // tCO2e
      } else if (row.type === "RES") {
        // If PPA/guarantee, emissions = 0; else use non-RES factor
        if (row.hasPPA === "YES") return 0
        return +((kWh * emissionFactorNonRES) / 1000).toFixed(3)
      }
    }
    return 0
  }

  const total = rows.reduce((sum, row) => sum + calculateEmissions(row), 0)

  useEffect(() => {
    if (onTotalChange) onTotalChange(total)
    // Check if any RES row has PPA/Guarantee YES
    const resPpaAvailable = rows.some((row) => row.type === "RES" && row.hasPPA === "YES")
    setEmissions((prev) => ({
      ...prev,
      scope2: {
        ...prev.scope2,
        electricity: total,
        total: total + prev.scope2.district,
      },
      resPpaAvailable,
    }))
  }, [total, rows])

  const filteredColumns = columns.map((col) => ({
    ...col,
    type: col.type as "number" | "select" | "text" | "action",
  })) as Column[]

  return (
    <div>
      <p className="mb-2 rounded border border-green-600 bg-background px-3 py-2 text-sm font-medium text-foreground">
        {t("annualConsumptionNote")}
      </p>
      <p className="mb-4 text-sm text-gray-300 dark:text-gray-400">
        {tSchema("sectionDescriptions.scope2Electricity")}
      </p>
      <div className="mb-4 overflow-x-auto">
        <DynamicTable<ElectricityRow>
          columns={filteredColumns}
          rows={rows}
          onRowChange={(rowIndex, key, value) => updateRow(rowIndex, key, value as string)}
          onRowDelete={deleteRow}
          getSelectOptions={(col) => {
            return col.options!
          }}
          extraColumns={[
            (row: ElectricityRow) => {
              const val = calculateEmissions(row)
              return val ? formatNumber(val) : "-"
            },
          ]}
          extraColumnLabels={[t("totalEmissionsColumn")]}
          renderCell={(col, row) => {
            const hasConsumption = !!row.consumption
            const hasCost = !!row.cost
            if (col.id === "consumption" && hasCost) {
              return <span className="text-gray-400"></span>
            }
            if (col.id === "unit" && hasCost) {
              return <span className="text-gray-400"></span>
            }
            if (col.id === "cost" && hasConsumption) {
              return <span className="text-gray-400"></span>
            }
            if (col.id === "hasPPA") {
              // Only show hasPPA for RES, else N/A
              if (row.type !== "RES") {
                return <span className="text-gray-400"></span>
              }
            }
            return undefined
          }}
        />
        <div className="my-4 flex items-center justify-between">
          <span className="text-base font-bold text-primary">{t("sectionTotal", { total: formatNumber(total) })}</span>
          <Button onClick={addRow}>+ {t("addRow")}</Button>
        </div>
      </div>
    </div>
  )
}

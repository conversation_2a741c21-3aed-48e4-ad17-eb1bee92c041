/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import React from "react"
import { Car, Globe, TreePine } from "lucide-react"
import { useTranslations } from "next-intl"

type ComparisonCardProps = {
  icon: React.ReactNode
  value: string | number
  description: string
}

const ComparisonCard: React.FC<ComparisonCardProps> = ({ icon, value, description }) => (
  <div className="flex min-w-[200px] flex-1 items-center px-2">
    <div className="mr-4 flex-shrink-0 text-green-700 dark:text-green-400">{icon}</div>
    <div className="flex flex-col items-start">
      <div className="text-2xl font-bold leading-tight text-green-900 dark:text-green-100">{value}</div>
      <div className="whitespace-pre-line text-xs text-green-900 dark:text-green-100">{description}</div>
    </div>
  </div>
)

export const ReportComparisons: React.FC<{
  trees: string | number
  kms: string | number
  globes: string | number
}> = ({ trees, kms, globes }) => {
  const t = useTranslations("ghgCalculator.report")

  return (
    <div className="mb-6 rounded border border-green-700 bg-green-100 dark:border-green-300 dark:bg-green-900">
      <div className="flex items-center px-4 py-2">
        <span className="mr-4 text-sm font-semibold text-green-900 dark:text-green-100">{t("comparableToTitle")}</span>
        <div className="flex flex-1 justify-between">
          <ComparisonCard icon={<TreePine size={32} />} value={trees} description={t("treesToAbsorb")} />
          <ComparisonCard icon={<Car size={32} />} value={kms} description={t("kmsDriven")} />
          <ComparisonCard icon={<Globe size={32} />} value={globes} description={t("timesAroundGlobe")} />
        </div>
      </div>
    </div>
  )
}

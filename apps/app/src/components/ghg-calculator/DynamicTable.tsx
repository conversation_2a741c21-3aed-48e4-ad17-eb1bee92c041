/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2025 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import React from "react"
import { reverseTranslateValue, translateColumns, translateOptions } from "@/lib/ghg-calculator/translateFormSchema"
import { Trash2 } from "lucide-react"
import { useTranslations } from "next-intl"

import { Button } from "@kreios/ui/button"
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from "@kreios/ui/select"

export enum ColumnType {
  Select = "select",
  Number = "number",
  Text = "text",
  Action = "action",
}

export type Column = {
  id: string
  label: string
  type: "select" | "number" | "text" | "action"
  options?: string[]
}

export type DynamicTableProps<RowType> = {
  columns: Column[]
  rows: RowType[]
  onRowChange: (rowIndex: number, key: keyof RowType, value: unknown) => void
  onRowDelete: (rowIndex: number) => void
  getSelectOptions?: (col: Column, row: RowType) => string[] | undefined // for dynamic options
  extraColumns?: (React.ReactNode | ((row: RowType, rowIndex: number) => React.ReactNode))[] // for custom columns like calculated emissions
  extraColumnLabels?: string[] // for custom extra column headers
  renderCell?: (col: Column, row: RowType, rowIndex: number) => React.ReactNode | undefined // for custom cell rendering
}

function ensureActionColumnLast(columns: Column[]): Column[] {
  // Remove any existing action column
  const filtered = columns.filter((col) => col.id !== "action")
  // Add action column at the end
  return [...filtered, { id: "action", label: "Action", type: "action" }]
}

export function DynamicTable<RowType extends Record<string, unknown>>({
  columns,
  rows,
  onRowChange,
  onRowDelete,
  getSelectOptions,
  extraColumns = [],
  extraColumnLabels = [],
  renderCell,
}: DynamicTableProps<RowType>) {
  const t = useTranslations("ghgCalculator.forms.common")
  // Translate columns before processing
  const translatedColumns = translateColumns(columns, t)
  const cols = ensureActionColumnLast(translatedColumns)

  return (
    <table className="w-full table-auto border border-border text-sm">
      <thead>
        <tr className="bg-muted">
          {/* Render all columns except action */}
          {cols
            .filter((col) => col.id !== "action")
            .map((col) => (
              <th
                key={col.id}
                className="border border-border px-3 py-2 text-center text-base font-semibold text-foreground"
              >
                {col.label}
              </th>
            ))}
          {/* Render extra column labels if provided */}
          {extraColumns.length > 0 &&
            extraColumns.map((_, i) => (
              <th
                key={`extra-${i}`}
                className="border border-border px-3 py-2 text-center text-base font-semibold text-foreground"
              >
                {extraColumnLabels[i] || ""}
              </th>
            ))}
          {/* Render action column last */}
          <th
            key="action"
            className="border border-border px-3 py-2 text-center text-base font-semibold text-foreground"
          >
            {t("action")}
          </th>
        </tr>
      </thead>
      <tbody>
        {rows.length === 0 ? (
          <tr>
            <td
              colSpan={cols.length + extraColumns.length}
              className="border border-border px-3 py-6 text-center text-muted-foreground"
            >
              {t("noRowsMessage")}
            </td>
          </tr>
        ) : (
          rows.map((row, rowIndex) => (
            <tr key={rowIndex} className={rowIndex % 2 === 0 ? "bg-background" : "bg-muted/50"}>
              {/* Render all columns except action */}
              {cols
                .filter((col) => col.id !== "action")
                .map((col) => {
                  // Custom cell rendering
                  if (renderCell) {
                    const custom = renderCell(col, row, rowIndex)
                    if (custom !== undefined)
                      return (
                        <td
                          key={col.id}
                          className="cursor-not-allowed border border-border bg-muted px-3 py-2 align-middle text-muted-foreground"
                        >
                          {custom}
                        </td>
                      )
                  }
                  // Default rendering
                  if (col.type === "select") {
                    const originalColumn = columns.find((c) => c.id === col.id)
                    const originalOptions = getSelectOptions?.(originalColumn!, row) ?? originalColumn?.options ?? []
                    const translatedOptions = translateOptions(col.id, originalOptions, t)

                    // Get current stored value (in English) and translate it for display
                    const storedValue = typeof row[col.id] === "string" ? (row[col.id] as string) : ""
                    const displayValue = storedValue ? translateOptions(col.id, [storedValue], t)[0] || storedValue : ""

                    return (
                      <td key={col.id} className="border border-border px-3 py-2 align-middle">
                        <Select
                          value={displayValue}
                          onValueChange={(translatedValue) => {
                            // Convert translated value back to original English value before storing
                            const originalValue = reverseTranslateValue(col.id, translatedValue, originalOptions, t)
                            onRowChange(rowIndex, col.id as keyof RowType, originalValue)
                          }}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder={t("selectOption")} />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              {translatedOptions.map((translatedOpt) => (
                                <SelectItem key={translatedOpt} value={translatedOpt}>
                                  {translatedOpt}
                                </SelectItem>
                              ))}
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      </td>
                    )
                  } else if (col.type === "number") {
                    return (
                      <td key={col.id} className="border border-border px-3 py-2 align-middle">
                        <input
                          type="number"
                          value={typeof row[col.id] === "string" ? (row[col.id] as string) : ""}
                          onChange={(e) => onRowChange(rowIndex, col.id as keyof RowType, e.target.value)}
                          className="w-full min-w-0 rounded border border-input bg-background px-2 py-1 text-left text-foreground hover:bg-accent focus:outline-none focus:ring-2 focus:ring-ring"
                        />
                      </td>
                    )
                  } else if (col.type === "text") {
                    return (
                      <td key={col.id} className="border border-border px-3 py-2 align-middle">
                        <input
                          type="text"
                          value={typeof row[col.id] === "string" ? (row[col.id] as string) : ""}
                          onChange={(e) => onRowChange(rowIndex, col.id as keyof RowType, e.target.value)}
                          className="w-full min-w-0 rounded border border-input bg-background px-2 py-1 text-foreground hover:bg-accent focus:outline-none focus:ring-2 focus:ring-ring"
                        />
                      </td>
                    )
                  }
                  // fallback
                  return (
                    <td key={col.id} className="border border-border px-3 py-2 align-middle text-foreground">
                      -
                    </td>
                  )
                })}
              {/* Render extra columns (e.g., emissions) */}
              {extraColumns.length > 0 &&
                extraColumns.map((colNode, i) => (
                  <td key={`extra-${i}`} className="border border-border px-3 py-2 align-middle text-foreground">
                    {typeof colNode === "function" ? colNode(row, rowIndex) : colNode}
                  </td>
                ))}
              {/* Render action column last */}
              <td key="action" className="border border-border px-3 py-2 text-center align-middle">
                <Button variant="ghost" onClick={() => onRowDelete(rowIndex)} aria-label="Delete row">
                  <Trash2 className="h-5 w-5 text-red-600 dark:text-red-400" />
                </Button>
              </td>
            </tr>
          ))
        )}
      </tbody>
    </table>
  )
}

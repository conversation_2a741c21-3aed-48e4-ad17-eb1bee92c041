/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { ReactNode } from "react"
import * as React from "react"
import {
  useHandlePrefilledAction,
  useIsPrefilledDisabled,
  useMountPrefilledField,
  usePrefilledForm,
  useShowPrefilledField,
} from "@/components/survey-form/use-survey-context"
import { useTranslations } from "next-intl"

import { Button } from "@kreios/ui/button"
import { Form } from "@kreios/ui/form"

type PrefilledFieldProps = {
  name: Parameters<typeof useShowPrefilledField>[0]
  children: ReactNode
  isArrayTable?: boolean
}

export const PrefilledField = React.memo(function PrefilledField({
  name,
  children,
  isArrayTable = false,
}: PrefilledFieldProps) {
  const t = useTranslations("survey.components.prefilledField")
  const form = usePrefilledForm()
  const handlePrefilledAction = useHandlePrefilledAction()
  const showPrefilledField = useShowPrefilledField(name)
  const isPrefilledDisabled = useIsPrefilledDisabled()

  const handleAccept = React.useCallback(() => {
    handlePrefilledAction(name, "accept")
  }, [handlePrefilledAction, name])

  const handleReject = React.useCallback(() => {
    handlePrefilledAction(name, "reject")
  }, [handlePrefilledAction, name])

  useMountPrefilledField(name, showPrefilledField)
  if (!showPrefilledField) return <>{children}</>

  return (
    <div className="my-2 space-y-2 rounded-lg border-2 border-orange-300 p-2 dark:border-orange-700">
      <div className="text-sm text-orange-600 dark:text-orange-400">{t("hasPrefilledValue")}</div>
      {isArrayTable ? (
        // Array table layout: input field above (full width), buttons below (50% each)
        <div className="space-y-3">
          <div className="w-full overflow-auto">
            <Form readOnly={true} {...form}>
              {children}
            </Form>
          </div>
          <div className="flex w-full gap-2">
            <Button
              type="button"
              variant="outline"
              className="flex-1 border-green-500 text-green-500 hover:bg-green-50 dark:border-green-600 dark:text-green-400 dark:hover:bg-green-900"
              onClick={handleAccept}
              disabled={isPrefilledDisabled}
            >
              {t("accept")}
            </Button>
            <Button
              type="button"
              variant="outline"
              className="flex-1 border-red-500 text-red-500 hover:bg-red-50 dark:border-red-600 dark:text-red-400 dark:hover:bg-red-900"
              onClick={handleReject}
              disabled={isPrefilledDisabled}
            >
              {t("reject")}
            </Button>
          </div>
        </div>
      ) : (
        // Default layout: input field and buttons on same line (horizontal)
        <div className="flex items-center gap-2 max-sm:flex-col max-sm:items-start">
          <div className="flex-1 overflow-auto">
            <Form readOnly={true} {...form}>
              {children}
            </Form>
          </div>
          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              className="border-green-500 text-green-500 hover:bg-green-50 dark:border-green-600 dark:text-green-400 dark:hover:bg-green-900"
              onClick={handleAccept}
              disabled={isPrefilledDisabled}
            >
              {t("accept")}
            </Button>
            <Button
              type="button"
              variant="outline"
              className="border-red-500 text-red-500 hover:bg-red-50 dark:border-red-600 dark:text-red-400 dark:hover:bg-red-900"
              onClick={handleReject}
              disabled={isPrefilledDisabled}
            >
              {t("reject")}
            </Button>
          </div>
        </div>
      )}
    </div>
  )
})

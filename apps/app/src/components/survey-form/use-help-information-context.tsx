/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { createContext, useContext, useState } from "react"

type HelpInformation = {
  title: React.ReactNode
  description: string
  exampleDocumentUrl?: string
}

type HelpInformationContextType = {
  helpInfo: HelpInformation | null
  setHelpInfo: (info: HelpInformation | null) => void
}

const HelpInformationContext = createContext<HelpInformationContextType | null>(null)

export function HelpInformationProvider({ children }: { children: React.ReactNode }) {
  const [helpInfo, setHelpInfo] = useState<HelpInformation | null>(null)

  return <HelpInformationContext.Provider value={{ helpInfo, setHelpInfo }}>{children}</HelpInformationContext.Provider>
}

export function useHelpInformation() {
  const context = useContext(HelpInformationContext)
  if (!context) {
    throw new Error("useHelpInformation must be used within a HelpInformationProvider")
  }
  return context
}

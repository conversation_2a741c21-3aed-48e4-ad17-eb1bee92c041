/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import type { TableField } from "@/components/survey-form/types"
import type { Language } from "@/utils/constants"
import type { ArrayPath, FieldArray, FieldArrayPath, FieldValues } from "react-hook-form"
import type { z } from "zod"
import { DetailDisplay } from "@/components/survey-form/detail-display"
import { PrefilledField } from "@/components/survey-form/prefilled-field"
import { useHelpInformation } from "@/components/survey-form/use-help-information-context"
import { PlusIcon, TrashIcon } from "lucide-react"
import { useLocale, useTranslations } from "next-intl"
import { useFieldArray, useFormContext } from "react-hook-form"

import { cn } from "@kreios/ui"
import { But<PERSON> } from "@kreios/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@kreios/ui/table"

import { GenericFieldRenderer } from "./generic-field-renderer"

interface GenericFieldArrayProps<TFieldValues extends FieldValues> {
  field: TableField
  readOnly: boolean
  schema: z.ZodType<TFieldValues>
  hideQuestionNumber?: boolean
  addMargin?: boolean
}

export const GenericFieldArray = <TFieldValues extends FieldValues>({
  field,
  readOnly,
  schema,
  hideQuestionNumber = false,
  addMargin = false,
}: GenericFieldArrayProps<TFieldValues>) => {
  const { helpInfo } = useHelpInformation()
  const t = useTranslations("survey.components.genericFieldArray")
  const locale = useLocale() as Language

  const {
    field: name,
    columns,
    predefinedColumns,
    maxItems = 20,
    title,
    description,
    questionNumber,
    isYearlyTable = false,
    columnHeaders,
    rows,
    isEnergySourceTable = false,
    predefinedRows,
    itemLabel,
  } = field
  const { control } = useFormContext()

  const addItemLabel = `${t("add")} ${itemLabel?.[locale]}`
  const otherSourcesKey = `${name}.${name.split(".").pop()}_other_sources`
  const fieldName = (isEnergySourceTable ? otherSourcesKey : name) as FieldArrayPath<TFieldValues>

  const {
    fields: fieldItems,
    append,
    remove,
  } = useFieldArray({
    control,
    name: fieldName,
  })

  // Default item for array-based tables
  const arrayDefaultItem = columns.reduce((acc, field) => {
    const fieldName = field.field.split(".").pop() ?? ""
    return {
      ...acc,
      [fieldName]: null,
    }
  }, {}) as FieldArray<FieldValues, ArrayPath<TFieldValues>>

  // Select default item based on table type
  const defaultItem = arrayDefaultItem

  const sectionDesc = description?.[locale]
  const sectionTitle = hideQuestionNumber ? (title?.[locale] ?? "") : `${questionNumber}. ${title?.[locale] ?? ""}`

  return (
    <>
      <div className={cn(isYearlyTable ? "rounded-lg bg-blue-50/50 p-4 dark:bg-blue-900/30" : "", addMargin && "ml-6")}>
        <DetailDisplay
          className={isYearlyTable ? "!text-md mb-4 text-blue-600 dark:text-blue-400" : "mb-4"}
          label={sectionTitle}
          helpInfo={
            sectionDesc
              ? {
                  title: sectionTitle,
                  description: sectionDesc,
                }
              : undefined
          }
        />

        {isEnergySourceTable && predefinedRows ? (
          // Render energy-source-table (e.g., non_renewable_sources)
          <div className="space-y-6 rounded-lg bg-gray-50 p-6 dark:bg-gray-800 md:w-fit">
            {/* Predefined Rows (district_heating, gas, etc.) */}
            {predefinedRows.map((row) => (
              <PrefilledField name={`${name}.${row.key}`} key={row.key}>
                <div className="space-y-2 rounded-lg bg-white p-4 dark:bg-gray-900">
                  <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
                    <div className="flex gap-2 pt-2">
                      <span className="text-sm">
                        <DetailDisplay
                          label={row.questionNumber}
                          helpInfo={{
                            title: row.title?.[locale] ?? "",
                            description: row.description?.[locale] ?? "",
                          }}
                        />
                      </span>
                    </div>
                    <div className="flex flex-1 flex-col items-center gap-4 sm:flex-row">
                      {predefinedColumns && predefinedColumns.length > 0 ? (
                        predefinedColumns
                          .filter((col) => col.field.startsWith(`${name}.${row.key}.`))
                          .map((col) => {
                            const fieldName = col.field.split(".").pop()
                            if (!fieldName) return null
                            const fieldPath = `${name}.${row.key}.${fieldName}`

                            return (
                              <div key={`${row.key}-${fieldName}`} className={"w-full"}>
                                {col.type !== "checkbox" && (
                                  <div className="mb-2 text-sm font-medium">{col.title?.[locale] ?? ""}</div>
                                )}
                                <GenericFieldRenderer
                                  field={{
                                    ...col,
                                    field: fieldPath,
                                    questionNumber: row.questionNumber,
                                    placeholder: col.placeholder,
                                    title: col.title,
                                  }}
                                  name={fieldPath}
                                  readOnly={readOnly}
                                  hideLabel
                                  hidePrefilledField
                                  schema={schema}
                                />
                              </div>
                            )
                          })
                      ) : (
                        <div className="text-sm text-gray-500 dark:text-gray-400">{t("noPredefinedColumns")}</div>
                      )}
                    </div>
                  </div>
                </div>
              </PrefilledField>
            ))}

            {/* Other Sources (dynamic array) */}
            {fieldItems.map((field, index) => (
              <div key={field.id} className="space-y-2 rounded-lg bg-white p-4 dark:bg-gray-900">
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
                  <div className="flex gap-2 pt-2">
                    <span className="text-sm">
                      <DetailDisplay label={`${questionNumber}.${predefinedRows.length + index + 1}`} />
                    </span>
                  </div>
                  <div className="flex flex-1 flex-col items-center gap-4 sm:flex-row">
                    {columns.map((col) => {
                      const fieldName = col.field.split(".").pop()
                      const othersourceKey = `${name.split(".").pop()}_other_sources`
                      if (!fieldName) return null
                      const fieldPath = `${name}.${othersourceKey}.${index}.${fieldName}`

                      return (
                        <div key={`${field.id}-${fieldName}`} className={col.type === "checkbox" ? "" : "w-full"}>
                          {col.type !== "checkbox" && (
                            <div className="mb-2 text-sm font-medium">{col.title?.[locale] ?? ""}</div>
                          )}
                          <GenericFieldRenderer
                            field={{
                              ...col,
                              field: fieldPath,
                              questionNumber: `${questionNumber}.${predefinedRows.length + index + 1}`,
                              placeholder: col.placeholder,
                              title: col.title,
                            }}
                            name={fieldPath}
                            readOnly={readOnly}
                            hideLabel
                            fieldClassName={fieldName === "type" ? "flex-1" : undefined}
                            schema={schema}
                          />
                        </div>
                      )
                    })}
                  </div>
                </div>
                {!readOnly && (
                  <div className="flex justify-end space-y-2">
                    <div className="flex justify-end space-y-2">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => remove(index)}
                        className="text-red-500 hover:bg-red-50 hover:text-red-600 dark:text-red-400 dark:hover:bg-red-900/30 dark:hover:text-red-300"
                      >
                        <TrashIcon className="mr-2 h-4 w-4" />
                        {t("removeSource")}
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            ))}
            {!readOnly && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => append(defaultItem, { shouldFocus: false })}
                className="mt-2"
              >
                <PlusIcon className="mr-2 h-4 w-4" />
                {addItemLabel}
              </Button>
            )}
          </div>
        ) : isYearlyTable && rows && columnHeaders ? (
          // Render yearly-table
          <div className="relative pl-6 before:absolute before:bottom-0 before:left-0 before:top-0 before:w-1 before:bg-blue-500 dark:before:bg-blue-600">
            <div className="rounded-lg bg-white p-4 dark:bg-gray-900">
              <Table>
                <TableHeader>
                  <TableRow className="border-b border-gray-200">
                    <TableHead className="w-16 text-gray-500">{t("no")}</TableHead>
                    <TableHead className="text-gray-500">{t("category")}</TableHead>
                    {columnHeaders.map((header) => (
                      <TableHead key={header} className="text-gray-500">
                        {header}
                      </TableHead>
                    ))}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {rows.map((row) => (
                    <TableRow key={row.key} className="border-b border-gray-200">
                      <TableCell className="w-16">{row.questionNumber}</TableCell>
                      <TableCell className="" colSpan={row.isHeader ? columnHeaders.length : 1}>
                        <DetailDisplay
                          label={row.title?.[locale] ?? ""}
                          helpInfo={{
                            title: row.title?.[locale] ?? "",
                            description: row.description?.[locale] ?? "",
                          }}
                          className="!whitespace-break-spaces"
                        />
                      </TableCell>
                      {!row.isHeader &&
                        columnHeaders.map((year, index) => {
                          const yearField = columns.find((f) => f.field === `year_${year}`)
                          const key = row.key.split(".").pop()

                          return (
                            <TableCell key={year}>
                              {yearField ? (
                                <GenericFieldRenderer
                                  field={{
                                    ...yearField,
                                    field: `${name}.${row.key}.${key}_year_${year}`,
                                    questionNumber: `${row.questionNumber}.${index + 1}`,
                                    //todo
                                    placeholder: row.title,
                                  }}
                                  name={`${name}.${row.key}.${key}_year_${year}`}
                                  readOnly={readOnly}
                                  hideLabel
                                  fieldClassName="w-full rounded-md border-gray-200"
                                  hidePrefilledField
                                  schema={schema}
                                />
                              ) : (
                                <span>{t("yearNotFound")}</span>
                              )}
                            </TableCell>
                          )
                        })}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        ) : (
          // Render array-based table
          <div className="space-y-6 rounded-lg bg-gray-50 p-4 dark:bg-gray-800">
            {fieldItems.map((item, index) => (
              <div key={item.id} className="w-full space-y-6 rounded-lg bg-white p-4 dark:bg-gray-900">
                <div className="grid w-full grid-cols-1 gap-6">
                  <h3 className="text-lg font-semibold">{`${itemLabel?.[locale]} ${index + 1}`}</h3>

                  <div
                    className={cn(
                      "grid grid-cols-1 gap-6 sm:grid-cols-1 md:grid-cols-2",
                      helpInfo ? "xl:grid-cols-2" : "xl:grid-cols-3"
                    )}
                  >
                    {columns.map((field) => {
                      const fieldName = field.field.split(".").pop()
                      if (!fieldName) return null

                      return (
                        <div className="w-full">
                          <GenericFieldRenderer
                            key={`${item.id}-${fieldName}`}
                            field={{
                              ...field,
                              field: `${name}.${index}.${fieldName}`,
                              questionNumber: `${questionNumber}.${index + 1}`,
                            }}
                            name={`${name}.${index}.${fieldName}`}
                            readOnly={readOnly}
                            hideQuestionNumber
                            schema={schema}
                            isArrayTable={true}
                          />
                        </div>
                      )
                    })}
                  </div>
                  {index > 0 && !readOnly && (
                    <div className="flex justify-end">
                      <Button
                        type="button"
                        variant="outline"
                        className="border-gray-200 text-red-500 hover:text-red-600 dark:border-gray-700 dark:text-red-400 dark:hover:text-red-300"
                        onClick={() => remove(index)}
                      >
                        <TrashIcon className="mr-2 h-4 w-4" />
                        {t("remove")}
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            ))}
            {fieldItems.length < maxItems && !readOnly && (
              <Button
                type="button"
                variant="outline"
                className="flex items-center gap-2 rounded-lg border border-gray-200 px-4 py-2 text-sm dark:border-gray-700"
                onClick={() => append(defaultItem, { shouldFocus: false })}
              >
                <PlusIcon className="h-4 w-4" />
                {addItemLabel}
              </Button>
            )}
          </div>
        )}
      </div>
    </>
  )
}

/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import type { FormField as FieldType } from "@/components/survey-form/types"
import type { Language } from "@/utils/constants"
import type { FieldValues, Path, PathValue } from "react-hook-form"
import type { z } from "zod"
import React, { useEffect } from "react"
import { ActivitySelector } from "@/components/survey-form/activity-selector"
import { DetailDisplay } from "@/components/survey-form/detail-display"
import { FileUploadField } from "@/components/survey-form/file-upload-field"
import { PrefilledField } from "@/components/survey-form/prefilled-field"
import { useLocale, useTranslations } from "next-intl"
import { useFormContext } from "react-hook-form"

import { cn } from "@kreios/ui"
import { createF<PERSON><PERSON><PERSON>H<PERSON><PERSON> } from "@kreios/ui/form/form-field-helper"
import { MultiSelectorContent, MultiSelectorItem } from "@kreios/ui/multi-select"
import { SelectItem } from "@kreios/ui/select"

interface GenericFieldRendererProps<TFieldValues extends FieldValues> {
  field: FieldType
  name: string
  readOnly?: boolean
  hideLabel?: boolean
  fieldClassName?: string
  hidePrefilledField?: boolean
  hideQuestionNumber?: boolean
  schema: z.ZodType<TFieldValues>
  addMargin?: boolean
  isArrayTable?: boolean
}

export const GenericFieldRenderer = <TFieldValues extends FieldValues>({
  field,
  name,
  readOnly = false,
  hideLabel = false,
  fieldClassName = "",
  hideQuestionNumber = false,
  hidePrefilledField = false,
  schema,
  addMargin = false,
  isArrayTable = false,
}: GenericFieldRendererProps<TFieldValues>) => {
  const { control } = useFormContext()

  const t = useTranslations("common")
  const FormField = React.useMemo(() => createFormFieldHelper(schema), [])
  const locale = useLocale() as Language
  const { setValue, getValues } = useFormContext<TFieldValues>()
  const fieldName = name as Path<TFieldValues>

  useEffect(() => {
    if ("enum" in field && Array.isArray(field.enum)) {
      const currentValue = getValues(fieldName)

      if (field.type === "multiselect" && Array.isArray(currentValue)) {
        const newValues = (currentValue as string[]).map((val: string) => {
          const match = field.enum.find((opt) => Object.values(opt).includes(val))
          return match ? match[locale] : val
        })

        const isChanged = JSON.stringify(currentValue) !== JSON.stringify(newValues)
        if (isChanged) {
          setValue(fieldName, newValues as PathValue<TFieldValues, Path<TFieldValues>>)
        }
      } else {
        const match = field.enum.find((opt) => Object.values(opt).includes(currentValue))
        const newLocalizedValue = match?.[locale] as PathValue<TFieldValues, Path<TFieldValues>>

        if (match && newLocalizedValue !== currentValue) {
          if (field.type === "select") {
            setTimeout(() => {
              setValue(fieldName, newLocalizedValue)
            }, 0)
          } else {
            setValue(fieldName, newLocalizedValue)
          }
        }
      }
    }
  }, [locale, field, fieldName, getValues, setValue])

  // Hide placeholders when in readonly mode to avoid showing example values like "(e.g. 2024)"
  const shouldShowPlaceholder = !readOnly
  const placeholder = shouldShowPlaceholder
    ? field.type === "select"
      ? t("selectPlaceholder")
      : field.placeholder?.[locale]
    : ""
  const title = field.title?.[locale] ?? ""
  const description = field.description?.[locale]
  const helpText = field.helpText?.[locale]

  const renderField = React.useCallback(() => {
    switch (field.type) {
      case "date":
        return <FormField.DatePicker name={fieldName} label="" disabled={readOnly} />

      case "checkbox":
        return <FormField.Checkbox name={fieldName} label={title} disabled={readOnly} />

      case "file":
        return <FileUploadField field={field} schema={schema} control={control} isReadOnly={readOnly} />

      case "textarea":
        return <FormField.Textarea name={fieldName} label="" placeholder={placeholder} disabled={readOnly} autoGrow />

      case "select":
        return (
          <FormField.Select
            name={fieldName}
            label=""
            placeholder={placeholder}
            disabled={readOnly}
            className={cn(isArrayTable ? "w-full" : "max-w-[250px]", fieldClassName)}
          >
            {field.enum.map((option, index) => (
              <SelectItem key={index} value={option[locale]}>
                {option[locale]}
              </SelectItem>
            ))}
          </FormField.Select>
        )

      case "activity-selector":
        return <ActivitySelector name={name} activities={field.enum} required={field.required} disabled={readOnly} />

      case "multiselect":
        return (
          <FormField.MultiSelect
            name={fieldName}
            label=""
            className={isArrayTable ? "w-full" : "max-w-[450px]"}
            placeholder={placeholder}
            disabled={readOnly}
          >
            <MultiSelectorContent>
              {field.enum.map((option, index) => (
                <MultiSelectorItem key={index} value={option[locale]}>
                  {option[locale]}
                </MultiSelectorItem>
              ))}
            </MultiSelectorContent>
          </FormField.MultiSelect>
        )
      case "boolean-radio":
        return (
          <FormField.RadioCheckbox
            name={fieldName}
            label=""
            options={[
              { label: t("yes"), value: true },
              { label: t("no"), value: false },
            ]}
            allowUnselect
          />
        )
      case "radio":
        return (
          <FormField.RadioButtons
            name={fieldName}
            label=""
            options={field.enum.map((option) => ({
              label: option[locale],
              value: option[locale],
            }))}
          />
        )
      default:
        return (
          <FormField
            name={fieldName}
            label=""
            placeholder={placeholder}
            disabled={readOnly}
            className={cn(isArrayTable ? "w-full" : "max-w-[400px]", fieldClassName)}
            isYearlyField={field.isYearField}
            isFormInput
          />
        )
    }
  }, [
    field,
    readOnly,
    fieldClassName,
    t,
    placeholder,
    title,
    FormField,
    control,
    fieldName,
    schema,
    isArrayTable,
    locale,
    name,
  ])

  return (
    <div className={cn("", "flex-1", addMargin && "ml-6")}>
      {!hideLabel && (
        <DetailDisplay
          label={hideQuestionNumber || field.dependency ? title : `${field.questionNumber}. ${title}`}
          helpInfo={
            helpText || description
              ? {
                  title: title,
                  description: helpText ?? description ?? "",
                }
              : undefined
          }
          required={field.required}
        />
      )}
      {hidePrefilledField ? (
        renderField()
      ) : (
        <PrefilledField name={name} isArrayTable={isArrayTable}>
          {renderField()}
        </PrefilledField>
      )}
    </div>
  )
}

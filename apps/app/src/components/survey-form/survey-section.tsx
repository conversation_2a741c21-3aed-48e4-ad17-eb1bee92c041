/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
"use client"

import type { FormSection } from "@/components/survey-form/types"
import type { Language } from "@/utils/constants"
import type { FieldValues } from "react-hook-form"
import type { z } from "zod"
import { GenericFormSection } from "@/components/survey-form/generic-form-section"
import { useLocale } from "next-intl"

import { useReadOnly } from "@kreios/ui/form"

interface SelfAssessmentSectionProps<TFieldValues extends FieldValues> {
  sectionFields: FormSection[]
  zodSchema: z.ZodType<TFieldValues>
}

export const SurveySection = <TFieldValues extends FieldValues>({
  sectionFields,
  zodSchema,
}: SelfAssessmentSectionProps<TFieldValues>) => {
  const readOnly = useReadOnly()
  const locale = useLocale() as Language
  // const selfAssessmentFormSchema = getSelfAssessmentFormSchema(locale)

  if (sectionFields[0]?.sectionTitle?.en === "Introduction") {
    return <div className="mb-2 whitespace-pre-line">{sectionFields[0]?.sectionDescription?.[locale]}</div>
  }

  return (
    <div className="w-full space-y-8">
      {sectionFields.map((section, index) => (
        <GenericFormSection key={index} section={section} readOnly={readOnly} schema={zodSchema} />
      ))}
    </div>
  )
}

/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { useShowPrefilledField } from "@/components/survey-form/use-survey-context"
import {
  useHandlePrefilledAction,
  useIsAtLeastOnePrefilledFieldMounted,
  useIsPrefilledDisabled,
} from "@/components/survey-form/use-survey-context"
import { useTranslations } from "next-intl"

import { Button } from "@kreios/ui/button"

type HasDot<T extends string> = T extends `${infer _}.${infer _}` ? true : false
type FilterNoDots<T extends string> = T extends string ? (HasDot<T> extends true ? never : T) : never

type SectionId = FilterNoDots<Parameters<typeof useShowPrefilledField>[0]>

export const PrefilledHeader = ({ sectionId }: { sectionId: SectionId }) => {
  const handleSectionBulkAction = useHandlePrefilledAction()
  const hasSectionPrefilledData = useIsAtLeastOnePrefilledFieldMounted()
  const isPrefilledDisabled = useIsPrefilledDisabled()
  const t = useTranslations("survey.components.prefilledHeader")

  // Don't render anything if there are no pending prefilled values
  if (!hasSectionPrefilledData) {
    return null
  }
  return (
    <div className="mb-6 flex items-center justify-between gap-4 rounded-lg border border-blue-300 bg-blue-100 p-4 dark:border-blue-700 dark:bg-blue-900 max-sm:flex-col">
      <p className="mr-4 flex-1 text-blue-800 dark:text-blue-200">{t("message")}</p>
      <div className="flex space-x-2">
        <Button
          onClick={() => handleSectionBulkAction(sectionId, "accept")}
          variant="outline"
          className="border-green-500 text-green-500 hover:bg-green-50 dark:border-green-600 dark:text-green-400 dark:hover:bg-green-900"
          disabled={isPrefilledDisabled}
        >
          {t("acceptAll")}
        </Button>
        <Button
          onClick={() => handleSectionBulkAction(sectionId, "reject")}
          variant="outline"
          className="border-red-500 text-red-500 hover:bg-red-50 dark:border-red-600 dark:text-red-400 dark:hover:bg-red-900"
          disabled={isPrefilledDisabled}
        >
          {t("rejectAll")}
        </Button>
      </div>
    </div>
  )
}

/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2025 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
"use client"

import type { ComponentProps, FC } from "react"
import { usePostHogTracking } from "@/hooks/use-posthog-tracking"

import { SidebarUserMenu } from "@kreios/admin-layout/sidebar/sidebar-user-menu"

export const TrackedUserMenu: FC<{ user: ComponentProps<typeof SidebarUserMenu>["user"] }> = ({ user }) => {
  const { trackEvent } = usePostHogTracking()

  const trackLogout = () =>
    trackEvent("user_logout", {
      user_email: user.email,
      time: Date.now(),
    })

  const trackTheme = (theme: string | undefined) =>
    trackEvent("theme_switch", {
      current_theme: theme ?? "DEFAULT",
    })

  return <SidebarUserMenu user={user} trackLogout={trackLogout} trackTheme={trackTheme} />
}

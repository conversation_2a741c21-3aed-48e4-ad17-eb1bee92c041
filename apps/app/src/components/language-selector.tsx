/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

"use client"

import { useCallback, useEffect, useRef, useState } from "react"
import { useRouter } from "next/navigation"
import { env } from "@/env"
import { usePostHogTracking } from "@/hooks/use-posthog-tracking"
import { Language } from "@/utils/constants"
import { Globe } from "lucide-react"
import { useLocale } from "next-intl"
import { createPortal } from "react-dom"

import { cn } from "@kreios/ui"
import { Button } from "@kreios/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@kreios/ui/dropdown-menu"

interface LanguageSelectorProps {
  className?: string
}

export const LanguageSelector = ({ className }: LanguageSelectorProps) => {
  const router = useRouter()
  const currentLocale = useLocale() as Language
  const [isLoading, setIsLoading] = useState(false)
  const lastSwitchTimeRef = useRef<number | null>(null)
  const [timeoutDuration, setTimeoutDuration] = useState(3000) // Start with 3 seconds
  const [languageSelected, setLanguageSelected] = useState(false)
  const { trackEvent } = usePostHogTracking()

  // Safety timeout to ensure loading state is reset if page refresh takes too long
  useEffect(() => {
    if (isLoading) {
      const timeout = setTimeout(() => {
        setIsLoading(false)
      }, timeoutDuration)

      return () => clearTimeout(timeout)
    }
  }, [isLoading, timeoutDuration])

  // Track dropdown open/close
  const [dropdownOpenTime, setDropdownOpenTime] = useState<number | null>(null)

  const handleDropdownOpenChange = (isOpen: boolean) => {
    if (isOpen) {
      // Dropdown opened
      const openTime = Date.now()
      setDropdownOpenTime(openTime)

      trackEvent("language_dropdown_open", {
        current_language: currentLocale,
        trigger_location: "header",
        available_languages: ["en", "et"],
      })
    } else {
      // Dropdown closed
      const closeTime = Date.now()
      const timeOpen = dropdownOpenTime ? closeTime - dropdownOpenTime : 0

      trackEvent("language_dropdown_close", {
        current_language: currentLocale,
        trigger_location: "header",
        time_open_ms: timeOpen,
        available_languages: ["en", "et"],
        language_selected: languageSelected,
        close_reason: languageSelected ? "language_selected" : "dismissed",
      })

      setDropdownOpenTime(null)
      setLanguageSelected(false) // Reset for next time
    }
  }

  const onLanguageSelect = useCallback(
    async (language: Language) => {
      if (language === currentLocale) return

      try {
        const startTime = Date.now()
        const isFirstSwitch = lastSwitchTimeRef.current === null

        // Track language change start
        trackEvent("language_change", {
          previous_language: currentLocale,
          new_language: language,
          trigger_location: "header",
          user_initiated: true,
          is_first_switch: isFirstSwitch,
        })

        setIsLoading(true)
        await fetch("/api/set-language", {
          method: "POST",
          body: JSON.stringify({ locale: language }),
          headers: {
            "Content-Type": "application/json",
          },
        })
        router.refresh()

        // Store the time it took to switch languages
        const switchTime = Date.now() - startTime

        // Track language switch performance
        trackEvent("language_switch_performance", {
          switch_duration_ms: switchTime,
          timeout_duration_ms: timeoutDuration,
          is_first_switch: isFirstSwitch,
          previous_language: currentLocale,
          new_language: language,
        })

        // If we have a previous switch time, adjust the timeout duration
        // based on the average of the previous and current switch times
        if (lastSwitchTimeRef.current) {
          const avgTime = Math.ceil((lastSwitchTimeRef.current + switchTime) / 2)
          // Add a buffer of 1 seconds to the average time
          const newTimeout = avgTime + 1000
          // Ensure timeout is at least 3 seconds and at most 15 seconds
          setTimeoutDuration(Math.max(3000, Math.min(7000, newTimeout)))

          // Only log in development
          if (env.NODE_ENV === "development") {
            console.log(`Adjusted timeout to ${newTimeout}ms based on avg switch time of ${avgTime}ms`)
          }
        }

        lastSwitchTimeRef.current = switchTime

        // The loading state will persist until the page refreshes completely
        // We don't need to manually set it to false as the component will remount
      } catch (error) {
        console.error("Error changing language:", error)

        // Track language change error
        trackEvent("language_change_error", {
          previous_language: currentLocale,
          attempted_language: language,
          error_message: error instanceof Error ? error.message : "Unknown error",
          trigger_location: "header",
        })

        setIsLoading(false)
      }
    },
    [router, currentLocale, trackEvent, timeoutDuration]
  )
  const handleClick = (e: React.MouseEvent<HTMLDivElement, MouseEvent> | React.KeyboardEvent<HTMLDivElement>) => {
    if (e instanceof KeyboardEvent) {
      if (e.key === "Escape") {
        e.preventDefault()
        e.stopPropagation()
      }
    } else {
      e.preventDefault()
      e.stopPropagation()
    }
  }

  return (
    <>
      {isLoading &&
        typeof document !== "undefined" &&
        createPortal(
          <div
            className="fixed inset-0 z-[9999] flex items-center justify-center bg-black/30 backdrop-blur-sm dark:bg-black/50"
            style={{ width: "100vw", height: "100vh", position: "fixed", top: 0, left: 0 }}
            onClick={handleClick}
            onKeyDown={handleClick}
          >
            <div className="rounded-lg bg-white/10 p-6 backdrop-blur-md dark:bg-black/20">
              <div className="flex space-x-3">
                <div className="h-4 w-4 animate-bounce rounded-full bg-white [animation-delay:-0.3s] dark:bg-gray-200"></div>
                <div className="h-4 w-4 animate-bounce rounded-full bg-white [animation-delay:-0.15s] dark:bg-gray-200"></div>
                <div className="h-4 w-4 animate-bounce rounded-full bg-white dark:bg-gray-200"></div>
              </div>
            </div>
          </div>,
          document.body
        )}
      <DropdownMenu onOpenChange={handleDropdownOpenChange}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className={cn("text-gray-600 dark:text-white", className)}
            disabled={isLoading}
          >
            <Globe className="h-6 w-6" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem
            onClick={async () => {
              // Mark that a language was selected
              setLanguageSelected(true)

              // Track language option click (even if same language)
              trackEvent("language_option_click", {
                clicked_language: Language.ET,
                current_language: currentLocale,
                is_same_language: currentLocale === Language.ET,
                trigger_location: "header",
              })
              await onLanguageSelect(Language.ET)
            }}
            className="gap-2"
          >
            <span>🇪🇪</span> Eesti
            {currentLocale === Language.ET && <span className="ml-auto h-2 w-2 rounded-full bg-primary" />}
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={async () => {
              // Mark that a language was selected
              setLanguageSelected(true)

              // Track language option click (even if same language)
              trackEvent("language_option_click", {
                clicked_language: Language.EN,
                current_language: currentLocale,
                is_same_language: currentLocale === Language.EN,
                trigger_location: "header",
              })
              await onLanguageSelect(Language.EN)
            }}
            className="gap-2"
          >
            <span>🇬🇧</span> English
            {currentLocale === Language.EN && <span className="ml-auto h-2 w-2 rounded-full bg-primary" />}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  )
}

/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

// Average building energy intensity (kWh/m²/year) for offices (source: CIBSE, UK Gov)
const buildingEnergyIntensity = 200 // kWh/m²/year

// Area-based emission factors (kg CO₂e per m²/year)
const areaFactors: Record<string, Record<string, number>> = {
  "Natural gas": { "m²": buildingEnergyIntensity * 0.184, "ft²": (buildingEnergyIntensity * 0.184) / 10.7639 },
  Diesel: { "m²": buildingEnergyIntensity * 0.267, "ft²": (buildingEnergyIntensity * 0.267) / 10.7639 },
  LPG: { "m²": buildingEnergyIntensity * 0.214, "ft²": (buildingEnergyIntensity * 0.214) / 10.7639 },
  Propane: { "m²": buildingEnergyIntensity * 0.214, "ft²": (buildingEnergyIntensity * 0.214) / 10.7639 },
  "Biomass - wood chips": { "m²": buildingEnergyIntensity * 0.039, "ft²": (buildingEnergyIntensity * 0.039) / 10.7639 },
  "Biomass - wood pellets": {
    "m²": buildingEnergyIntensity * 0.039,
    "ft²": (buildingEnergyIntensity * 0.039) / 10.7639,
  },
  "Biomass - grass/straw": {
    "m²": buildingEnergyIntensity * 0.039,
    "ft²": (buildingEnergyIntensity * 0.039) / 10.7639,
  },
  "Heating oil": { "m²": buildingEnergyIntensity * 0.296, "ft²": (buildingEnergyIntensity * 0.296) / 10.7639 },
  "Biomass - wood logs": { "m²": buildingEnergyIntensity * 0.039, "ft²": (buildingEnergyIntensity * 0.039) / 10.7639 },
  "Coal (domestic)": { "m²": buildingEnergyIntensity * 0.341, "ft²": (buildingEnergyIntensity * 0.341) / 10.7639 },
  "Coal (industrial)": { "m²": buildingEnergyIntensity * 0.341, "ft²": (buildingEnergyIntensity * 0.341) / 10.7639 },
}

// Standard emission factors (kg CO2e/kWh) for stationary fuels (DEFRA 2022, IPCC, IEA typical values)
// Source: DEFRA 2022, IPCC, IEA
//   Natural gas:         0.184
//   Diesel:              0.267
//   LPG:                 0.214
//   Heating oil:         0.296
//   Coal (domestic):     0.341
//   Coal (industrial):   0.341
//   Biomass - wood chips:0.039
//   Biomass - wood logs: 0.039
//   Biomass - wood pellets: 0.039
//   Biomass - grass/straw: 0.039
//   Propane:             0.214
export const stationaryEmissionFactors: Record<string, number> = {
  "Natural gas": 0.184,
  Diesel: 0.267,
  LPG: 0.214,
  "Heating oil": 0.296,
  "Coal (domestic)": 0.341,
  "Coal (industrial)": 0.341,
  Propane: 0.214,
  "Biomass - wood logs": 0.039,
  "Biomass - wood chips": 0.039,
  "Biomass - wood pellets": 0.039,
  "Biomass - grass/straw": 0.039,
}

/**
 * Calculate stationary emissions from fuel consumption using DEFRA 2022 calorific values and densities.
 * @param fuel Fuel type (e.g., "Natural gas")
 * @param consumption Amount consumed
 * @param unit Unit of consumption (e.g., "litres", "kWh", "kg", "GJ", "tonnes")
 * @returns Emissions in tonnes CO₂e
 */
export function calculateStationaryEmissions(fuel: string, consumption: number, unit: string): number {
  const props = fuelProperties[fuel]
  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
  if (!props) {
    console.warn(`No fuel properties found for: ${fuel}`)
    return 0
  }

  let kWh = 0,
    kg = 0

  // Normalize unit to handle case variations from form schema
  const normalizedUnit = unit.toUpperCase()

  // First, handle direct energy units using toKWh
  if (["KWH", "MWH", "GWH", "GJ"].includes(normalizedUnit)) {
    kWh = toKWh(consumption, normalizedUnit)
  } else {
    // Fallback to calorific/density logic for mass/volume units
    switch (normalizedUnit) {
      case "KG":
        kg = consumption
        kWh = props.netCV_kWh_per_kg ? consumption * props.netCV_kWh_per_kg : 0
        break
      case "TONNES":
        kg = consumption * 1000
        kWh = props.netCV_kWh_per_kg ? consumption * 1000 * props.netCV_kWh_per_kg : 0
        break
      case "LITRES":
        if (props.density_kg_per_m3) {
          kg = (consumption / 1000) * props.density_kg_per_m3
          kWh = props.netCV_kWh_per_litre ? consumption * props.netCV_kWh_per_litre : 0
        }
        break
      case "M3":
        if (props.density_kg_per_m3) {
          kg = consumption * props.density_kg_per_m3
          kWh = props.netCV_kWh_per_kg ? kg * props.netCV_kWh_per_kg : 0
        }
        break
      default:
        console.warn(`Unsupported unit for stationary emissions: ${unit}`)
        return 0
    }
  }
  // Use the correct emission factor for each fuel
  const emissionFactorPerKWh = stationaryEmissionFactors[fuel] ?? 0
  if (emissionFactorPerKWh === 0) {
    console.warn(`No emission factor found for fuel: ${fuel}`)
  }
  const emission = kWh * emissionFactorPerKWh
  return +(emission / 1000).toFixed(3)
}

/**
 * Estimate emissions based on area (e.g., for buildings).
 * @param fuel Fuel type
 * @param area Area (m² or ft²)
 * @param areaUnit Area unit
 * @returns Emissions in tonnes CO₂e (annualized)
 */
export function estimateEmissionsByArea(fuel: string, area: number, areaUnit: string): number {
  const fuelFactors = areaFactors[fuel]
  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
  if (!fuelFactors) {
    console.warn(`No area factors found for fuel: ${fuel}`)
    return 0
  }

  // Normalize area unit to handle case variations from form schema
  const upperUnit = areaUnit.toUpperCase()
  const normalizedAreaUnit = upperUnit === "M2" ? "m²" : upperUnit === "FT2" ? "ft²" : areaUnit

  const factor = fuelFactors[normalizedAreaUnit]
  if (!factor) {
    console.warn(`No area factor found for fuel: ${fuel}, unit: ${areaUnit}`)
    return 0
  }
  // Convert kg CO₂e to tonnes
  return +((area * factor) / 1000).toFixed(3)
}

// IPCC 2006 default emission factors for enteric fermentation (kg CH₄/head/year)
export const entericCH4Factors: Record<string, number> = {
  "Dairy Cattle": 100,
  "Non-dairy cattle": 48,
  Sheep: 8,
  Swine: 1.5,
  Goats: 5,
  Horses: 18,
  Poultry: 0.1,
  Rabbit: 0.1,
}

// GWP for CH4 (AR4/AR5)
const GWP_CH4 = 25

/**
 * Calculate enteric fermentation emissions (tCO2e)
 */
export function calculateEntericFermentation(animal: string, count: number): number {
  const ef = entericCH4Factors[animal] || 0
  // kg CH4/year * GWP / 1000 = tCO2e
  return +((count * ef * GWP_CH4) / 1000).toFixed(3)
}

// IPCC default: 1% of N applied is emitted as N2O-N, then convert to N2O, then to CO2e
const EF_N2O_DIRECT = 0.01 // 1%
const N2O_N_TO_N2O = 44 / 28
const GWP_N2O = 298

// Typical N content for fertiliser (kg N per kg fertiliser)
export const fertiliserNContent: Record<string, number> = {
  "Inorganic Nitrogen fertilizers": 0.3, // 30% N
  "Organic Nitrogen fertilizers": 0.05, // 5% N
  Limestone: 0, // Not a direct N source
  Dolomite: 0, // Not a direct N source
  "Animal manure purposefully applied to soils": 0.02, // 2% N
}

// CO₂ emissions from liming materials (kg CO₂ per kg material)
// Based on IPCC 2006: 0.12 kg CO₂ per kg of limestone, 0.13 kg CO₂ per kg of dolomite
export const limingCO2Factors: Record<string, number> = {
  Limestone: 0.12, // kg CO₂ per kg limestone
  Dolomite: 0.13, // kg CO₂ per kg dolomite
}

/**
 * Calculate fugitive emissions (tCO2e) from refrigerant leakage or other gases.
 * Supports kg, tonnes, lbs, and is ready for future extension.
 */
export function calculateFugitiveEmissions(gas: string, amount: number, unit: string): number {
  const gwp = fugitiveGWP[gas] || 0
  if (gwp === 0) {
    console.warn(`No GWP value found for gas: ${gas}`)
  }

  // Convert amount to kg - normalize unit to uppercase for consistent matching
  let amountKg = amount
  const normalizedUnit = unit.toUpperCase()

  switch (normalizedUnit) {
    case "KG":
      amountKg = amount
      break
    case "TONNES":
      amountKg = amount * 1000
      break
    case "LBS":
      amountKg = amount * 0.453592
      break
    default:
      console.warn(`Unsupported unit for fugitive emissions: ${unit}`)
      return 0
  }
  // kg * GWP / 1000 = tCO2e
  return +((amountKg * gwp) / 1000).toFixed(3)
}

/**
 * Calculate direct N2O emissions from fertiliser (tCO2e), robust to kg, tonnes, lbs.
 */
export function calculateFertiliserEmissions(type: string, amount: number, unit = "kg"): number {
  // Convert amount to kg - normalize unit to uppercase for consistent matching
  let amountKg = amount
  const normalizedUnit = unit.toUpperCase()

  switch (normalizedUnit) {
    case "KG":
      amountKg = amount
      break
    case "TONNES":
      amountKg = amount * 1000
      break
    case "LBS":
      amountKg = amount * 0.453592
      break
    default:
      console.warn(`Unsupported unit for fertiliser emissions: ${unit}`)
      return 0
  }
  // Check if it's a liming material first
  if (limingCO2Factors[type]) {
    // Calculate CO₂ emissions from liming
    const co2Emitted = amountKg * limingCO2Factors[type] // kg CO₂
    // CO₂ has GWP = 1, so kg CO₂ = kg CO₂e
    return +(co2Emitted / 1000).toFixed(3) // Convert to tonnes
  }
  // Regular N₂O calculation for nitrogen fertiliser
  const nContent = fertiliserNContent[type] || 0
  const nApplied = amountKg * nContent // kg N
  const n2oEmitted = nApplied * EF_N2O_DIRECT * N2O_N_TO_N2O // kg N2O
  // kg N2O * GWP / 1000 = tCO2e
  return +((n2oEmitted * GWP_N2O) / 1000).toFixed(3)
}

// --- MOBILE COMBUSTION ---
// DEFRA/UK Gov 2023 emission factors (kg CO2e per unit)
export const mobileFuelFactors: Record<string, Record<string, number>> = {
  PETROL: { LITRES: 2.31, KG: 3.17, M3: 2.05, KWH: 0.184 },
  DIESEL: { LITRES: 2.68, KG: 3.16, M3: 2.68, KWH: 0.257 },
  LPG: { LITRES: 1.51, KG: 1.73, M3: 1.51, KWH: 0.214 },
}
// Distance-based (average car, DEFRA 2023)
export const mobileDistanceFactors: Record<string, Record<string, number>> = {
  CAR: { KM: 0.171, MILES: 0.275 },
  VAN: { KM: 0.234, MILES: 0.377 },
  HGV: { KM: 0.646, MILES: 1.04 },
  BUS: { KM: 0.105, MILES: 0.169 },
}
// Expense-based (very rough, EEIO)
const expenseFactorEUR = 0.4 // kg CO2e per EUR

// Standard emission factors (kg CO2e/kWh) for mobile fuels (DEFRA 2022, typical values)
//   petrol:   0.239
//   diesel:   0.257
//   lpg:      0.214
//   cng:      0.202
export const mobileFuelEmissionFactors: Record<string, number> = {
  PETROL: 0.239,
  DIESEL: 0.257,
  LPG: 0.214,
  CNG: 0.202,
}

/**
 * Calculate mobile emissions (tCO2e) for fuel-based method using DEFRA 2022 calorific values and densities.
 */
export function calculateMobileFuelEmissions(fuelType: string, amount: number, unit: string): number {
  const props = fuelProperties[fuelType.toUpperCase()]
  let kWh = 0,
    kg = 0
  switch (unit.toUpperCase()) {
    case "KWH":
      kWh = amount
      break
    case "GJ":
      kWh = amount * 277.78
      break
    case "KG":
      kg = amount
      kWh = props.netCV_kWh_per_kg ? amount * props.netCV_kWh_per_kg : 0
      break
    case "TONNES":
      kg = amount * 1000
      kWh = props.netCV_kWh_per_kg ? amount * 1000 * props.netCV_kWh_per_kg : 0
      break
    case "LITRES":
      // CNG is not valid in litres
      if (fuelType.toUpperCase() === "CNG") return 0
      if (props.density_kg_per_m3) {
        kg = (amount / 1000) * props.density_kg_per_m3
        kWh = props.netCV_kWh_per_litre ? amount * props.netCV_kWh_per_litre : 0
      }
      break
    case "M3":
      if (props.density_kg_per_m3) {
        kg = amount * props.density_kg_per_m3
        kWh = props.netCV_kWh_per_kg ? kg * props.netCV_kWh_per_kg : 0
      }
      break
    default:
      return 0
  }
  // Use the correct emission factor for each mobile fuel
  const fuelKey = fuelType.toUpperCase()
  const emissionFactorPerKWh = mobileFuelEmissionFactors[fuelKey] ?? 0
  const emission = kWh * emissionFactorPerKWh
  return +(emission / 1000).toFixed(3)
}

/**
 * Calculate mobile emissions (tCO2e) for distance-based method
 */
export function calculateMobileDistanceEmissions(category: string, distance: number, unit: string): number {
  // Map category to factor set
  let key = "CAR"
  if (/VAN|LCV/i.test(category)) key = "VAN"
  else if (/HGV|TRUCK/i.test(category)) key = "HGV"
  else if (/BUS|COACH/i.test(category)) key = "BUS"

  // Normalize unit to uppercase for consistent matching
  const normalizedUnit = unit.toUpperCase()
  const factor = mobileDistanceFactors[key][normalizedUnit]
  if (!factor) {
    console.warn(`No distance factor found for category: ${category}, unit: ${unit}`)
    return 0
  }
  return +((distance * factor) / 1000).toFixed(3)
}

/**
 * Calculate mobile emissions (tCO2e) for expense-based method
 */
export function calculateMobileExpenseEmissions(expense: number): number {
  return +((expense * expenseFactorEUR) / 1000).toFixed(3)
}

// --- FUGITIVE EMISSIONS ---
// GWP values (IPCC AR5)
export const fugitiveGWP: Record<string, number> = {
  R134a: 1430,
  R404A: 3922,
  R410A: 2088,
  R32: 675,
  SF6: 23900,
  CH4: 25,
  N2O: 298,
  CO2: 1,
}

// Updated calorific values and densities from DEFRA 2022 (conversion_factors.csv)
export const fuelProperties: Record<
  string,
  {
    netCV_GJ_per_tonne?: number
    grossCV_GJ_per_tonne?: number
    density_kg_per_m3?: number
    density_litres_per_tonne?: number
    netCV_kWh_per_kg?: number
    grossCV_kWh_per_kg?: number
    netCV_kWh_per_litre?: number
    grossCV_kWh_per_litre?: number
  }
> = {
  Diesel: {
    netCV_GJ_per_tonne: 42.88, // DEFRA 2022
    grossCV_GJ_per_tonne: 45.62,
    density_kg_per_m3: 841.04,
    density_litres_per_tonne: 1189.0,
    netCV_kWh_per_kg: 11.91,
    grossCV_kWh_per_kg: 12.67,
    netCV_kWh_per_litre: 10.02,
    grossCV_kWh_per_litre: 10.66,
  },
  PETROL: {
    netCV_GJ_per_tonne: 44.65,
    grossCV_GJ_per_tonne: 47.0,
    density_kg_per_m3: 741.84,
    density_litres_per_tonne: 1348.0,
    netCV_kWh_per_kg: 12.4,
    grossCV_kWh_per_kg: 13.06,
    netCV_kWh_per_litre: 9.2,
    grossCV_kWh_per_litre: 9.69,
  },
  LPG: {
    netCV_GJ_per_tonne: 45.94,
    grossCV_GJ_per_tonne: 49.33,
    density_kg_per_m3: 529.71,
    density_litres_per_tonne: 1887.84,
    netCV_kWh_per_kg: 12.76,
    grossCV_kWh_per_kg: 13.7,
    netCV_kWh_per_litre: 6.76,
    grossCV_kWh_per_litre: 7.26,
  },
  "Natural gas": {
    netCV_GJ_per_tonne: 45.2,
    grossCV_GJ_per_tonne: 50.08,
    density_kg_per_m3: 0.79,
    density_litres_per_tonne: 1259723.93,
    netCV_kWh_per_kg: 12.55,
    grossCV_kWh_per_kg: 13.91,
    netCV_kWh_per_litre: 0.01,
    grossCV_kWh_per_litre: 0.01,
  },
  "Heating oil": {
    netCV_GJ_per_tonne: 43.9,
    grossCV_GJ_per_tonne: 46.21,
    density_kg_per_m3: 800.64,
    density_litres_per_tonne: 1249.0,
    netCV_kWh_per_kg: 12.19,
    grossCV_kWh_per_kg: 12.84,
    netCV_kWh_per_litre: 9.76,
    grossCV_kWh_per_litre: 10.28,
  },
  "Biomass - wood chips": {
    netCV_GJ_per_tonne: 13.6,
    grossCV_GJ_per_tonne: 14.71,
    density_kg_per_m3: 253.0,
    netCV_kWh_per_kg: 3.78,
    grossCV_kWh_per_kg: 4.09,
  },
  "Biomass - wood logs": {
    netCV_GJ_per_tonne: 14.71,
    grossCV_GJ_per_tonne: 16.26,
    density_kg_per_m3: 425.0,
    netCV_kWh_per_kg: 4.09,
    grossCV_kWh_per_kg: 4.52,
  },
  "Biomass - wood pellets": {
    netCV_GJ_per_tonne: 17.28,
    grossCV_GJ_per_tonne: 18.69,
    density_kg_per_m3: 650.0,
    netCV_kWh_per_kg: 4.8,
    grossCV_kWh_per_kg: 5.19,
  },
  "Biomass - grass/straw": {
    netCV_GJ_per_tonne: 13.39,
    grossCV_GJ_per_tonne: 15.75,
    density_kg_per_m3: 160.0,
    netCV_kWh_per_kg: 3.72,
    grossCV_kWh_per_kg: 4.38,
  },
  Propane: {
    netCV_GJ_per_tonne: 46.35,
    grossCV_GJ_per_tonne: 50.33,
    density_kg_per_m3: 529.71,
    density_litres_per_tonne: 1887.84,
    netCV_kWh_per_kg: 12.88,
    grossCV_kWh_per_kg: 13.98,
    netCV_kWh_per_litre: 6.82,
    grossCV_kWh_per_litre: 7.41,
  },
  "Coal (domestic)": {
    netCV_GJ_per_tonne: 28.61,
    grossCV_GJ_per_tonne: 30.12,
    density_kg_per_m3: 850.0,
    netCV_kWh_per_kg: 7.95,
    grossCV_kWh_per_kg: 8.37,
    netCV_kWh_per_litre: 6.76,
    grossCV_kWh_per_litre: 7.11,
  },
  "Coal (industrial)": {
    netCV_GJ_per_tonne: 25.41,
    grossCV_GJ_per_tonne: 26.74,
    density_kg_per_m3: 800.0, // Approximate bulk density
    netCV_kWh_per_kg: 7.06,
    grossCV_kWh_per_kg: 7.43,
  },

  CNG: {
    netCV_GJ_per_tonne: 47.2, // 13.1 kWh/kg * 3.6 = 47.16 GJ/tonne
    netCV_kWh_per_kg: 13.1,
    density_kg_per_m3: 0.72,
  },
  DIESEL: {
    netCV_GJ_per_tonne: 42.88, // DEFRA 2022
    grossCV_GJ_per_tonne: 45.62,
    density_kg_per_m3: 841.04,
    density_litres_per_tonne: 1189.0,
    netCV_kWh_per_kg: 11.91,
    grossCV_kWh_per_kg: 12.67,
    netCV_kWh_per_litre: 10.02,
    grossCV_kWh_per_litre: 10.66,
  },
}

// Standard emission factors for Scope 2 electricity (kg CO2e/kWh)
// Source: DEFRA/IEA typical values
export const scope2ElectricityEmissionFactors = {
  nonRES: 0.233, // kg CO2e/kWh
  RES: 0, // kg CO2e/kWh (if PPA/guarantee)
  expensePerEUR: 0.4, // kg CO2e per EUR (fallback)
}

// Standard emission factors and area intensity for Scope 2 district energy (DEFRA/IEA typical values)
export const scope2DistrictEnergyConfig = {
  emissionFactors: {
    Heating: 0.198, // kg CO2e/kWh
    Cooling: 0.055, // kg CO2e/kWh
    Steam: 0.183, // kg CO2e/kWh
  },
  areaIntensity: {
    Heating: 200, // kWh/m²/year
    Cooling: 50, // kWh/m²/year
    Steam: 180, // kWh/m²/year
  },
}

// Shared utility: Convert any energy unit to kWh
export function toKWh(amount: number, unit: string): number {
  // Normalize to uppercase for consistent matching
  const normalizedUnit = unit.toUpperCase()

  switch (normalizedUnit) {
    case "KWH":
      return amount
    case "MWH":
      return amount * 1000
    case "GWH":
      return amount * 1_000_000
    case "GJ":
      return amount * 277.78
    default:
      console.warn(`Unsupported energy unit for conversion: ${unit}`)
      return 0
  }
}

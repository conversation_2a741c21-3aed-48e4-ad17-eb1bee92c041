/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import {
  buildingEnergyIntensity,
  EF_N2O_DIRECT,
  expenseFactorEUR,
  getEntericCH4Factor,
  getFertiliserNContent,
  getFuelProperties,
  getFugitiveGWP,
  getLimingCO2Factor,
  getMobileDistanceFactor,
  getMobileFuelEmissionFactor,
  getStationaryEmissionFactor,
  GWP_CH4,
  GWP_N2O,
  N2O_N_TO_N2O,
  stationaryEmissionFactors,
} from "./emission-factors-loader"

// Area-based emission factors (kg CO₂e per m²/year) - calculated dynamically
const getAreaFactors = (): Record<string, Record<string, number>> => {
  const areaFactors: Record<string, Record<string, number>> = {}

  // Generate area factors for each fuel using emission factors from JSON
  Object.keys(stationaryEmissionFactors).forEach((fuel) => {
    const emissionFactor = getStationaryEmissionFactor(fuel)
    areaFactors[fuel] = {
      "m²": buildingEnergyIntensity * emissionFactor,
      "ft²": (buildingEnergyIntensity * emissionFactor) / 10.7639,
    }
  })

  return areaFactors
}

// Export for backward compatibility
export { stationaryEmissionFactors }

/**
 * Calculate stationary emissions from fuel consumption using DEFRA 2022 calorific values and densities.
 * @param fuel Fuel type (e.g., "Natural gas")
 * @param consumption Amount consumed
 * @param unit Unit of consumption (e.g., "litres", "kWh", "kg", "GJ", "tonnes")
 * @returns Emissions in tonnes CO₂e
 */
export function calculateStationaryEmissions(fuel: string, consumption: number, unit: string): number {
  const props = getFuelProperties(fuel)

  if (!props) {
    console.warn(`No fuel properties found for: ${fuel}`)
    return 0
  }

  let kWh = 0,
    kg = 0

  // Normalize unit to handle case variations from form schema
  const normalizedUnit = unit.toUpperCase()

  // First, handle direct energy units using toKWh
  if (["KWH", "MWH", "GWH", "GJ"].includes(normalizedUnit)) {
    kWh = toKWh(consumption, normalizedUnit)
  } else {
    // Fallback to calorific/density logic for mass/volume units
    switch (normalizedUnit) {
      case "KG":
        kg = consumption
        kWh = props.netCV_kWh_per_kg ? consumption * props.netCV_kWh_per_kg : 0
        break
      case "TONNES":
        kg = consumption * 1000
        kWh = props.netCV_kWh_per_kg ? consumption * 1000 * props.netCV_kWh_per_kg : 0
        break
      case "LITRES":
        if (props.density_kg_per_m3) {
          kg = (consumption / 1000) * props.density_kg_per_m3
          kWh = props.netCV_kWh_per_litre ? consumption * props.netCV_kWh_per_litre : 0
        }
        break
      case "M3":
        if (props.density_kg_per_m3) {
          kg = consumption * props.density_kg_per_m3
          kWh = props.netCV_kWh_per_kg ? kg * props.netCV_kWh_per_kg : 0
        }
        break
      default:
        console.warn(`Unsupported unit for stationary emissions: ${unit}`)
        return 0
    }
  }
  // Use the correct emission factor for each fuel
  const emissionFactorPerKWh = stationaryEmissionFactors[fuel] ?? 0
  if (emissionFactorPerKWh === 0) {
    console.warn(`No emission factor found for fuel: ${fuel}`)
  }
  const emission = kWh * emissionFactorPerKWh
  return +(emission / 1000).toFixed(3)
}

/**
 * Estimate emissions based on area (e.g., for buildings).
 * @param fuel Fuel type
 * @param area Area (m² or ft²)
 * @param areaUnit Area unit
 * @returns Emissions in tonnes CO₂e (annualized)
 */
export function estimateEmissionsByArea(fuel: string, area: number, areaUnit: string): number {
  const areaFactors = getAreaFactors()
  const fuelFactors = areaFactors[fuel]

  if (!fuelFactors) {
    console.warn(`No area factors found for fuel: ${fuel}`)
    return 0
  }

  // Normalize area unit to handle case variations from form schema
  const upperUnit = areaUnit.toUpperCase()
  const normalizedAreaUnit = upperUnit === "M2" ? "m²" : upperUnit === "FT2" ? "ft²" : areaUnit

  const factor = fuelFactors[normalizedAreaUnit]
  if (!factor) {
    console.warn(`No area factor found for fuel: ${fuel}, unit: ${areaUnit}`)
    return 0
  }
  // Convert kg CO₂e to tonnes
  return +((area * factor) / 1000).toFixed(3)
}

// IPCC 2006 default emission factors for enteric fermentation (kg CH₄/head/year)
// Now loaded from JSON - export for backward compatibility
export { entericCH4Factors } from "./emission-factors-loader"

/**
 * Calculate enteric fermentation emissions (tCO2e)
 */
export function calculateEntericFermentation(animal: string, count: number): number {
  const ef = getEntericCH4Factor(animal)
  // kg CH4/year * GWP / 1000 = tCO2e
  return +((count * ef * GWP_CH4) / 1000).toFixed(3)
}

// IPCC default constants and fertiliser factors now loaded from JSON
// Export for backward compatibility
export { fertiliserNContent, limingCO2Factors } from "./emission-factors-loader"

/**
 * Calculate fugitive emissions (tCO2e) from refrigerant leakage or other gases.
 * Supports kg, tonnes, lbs, and is ready for future extension.
 */
export function calculateFugitiveEmissions(gas: string, amount: number, unit: string): number {
  const gwp = getFugitiveGWP(gas)
  if (gwp === 0) {
    console.warn(`No GWP value found for gas: ${gas}`)
  }

  // Convert amount to kg - normalize unit to uppercase for consistent matching
  let amountKg = amount
  const normalizedUnit = unit.toUpperCase()

  switch (normalizedUnit) {
    case "KG":
      amountKg = amount
      break
    case "TONNES":
      amountKg = amount * 1000
      break
    case "LBS":
      amountKg = amount * 0.453592
      break
    default:
      console.warn(`Unsupported unit for fugitive emissions: ${unit}`)
      return 0
  }
  // kg * GWP / 1000 = tCO2e
  return +((amountKg * gwp) / 1000).toFixed(3)
}

/**
 * Calculate direct N2O emissions from fertiliser (tCO2e), robust to kg, tonnes, lbs.
 */
export function calculateFertiliserEmissions(type: string, amount: number, unit = "kg"): number {
  // Convert amount to kg - normalize unit to uppercase for consistent matching
  let amountKg = amount
  const normalizedUnit = unit.toUpperCase()

  switch (normalizedUnit) {
    case "KG":
      amountKg = amount
      break
    case "TONNES":
      amountKg = amount * 1000
      break
    case "LBS":
      amountKg = amount * 0.453592
      break
    default:
      console.warn(`Unsupported unit for fertiliser emissions: ${unit}`)
      return 0
  }
  // Check if it's a liming material first
  const limingFactor = getLimingCO2Factor(type)
  if (limingFactor > 0) {
    // Calculate CO₂ emissions from liming
    const co2Emitted = amountKg * limingFactor // kg CO₂
    // CO₂ has GWP = 1, so kg CO₂ = kg CO₂e
    return +(co2Emitted / 1000).toFixed(3) // Convert to tonnes
  }
  // Regular N₂O calculation for nitrogen fertiliser
  const nContent = getFertiliserNContent(type)
  const nApplied = amountKg * nContent // kg N
  const n2oEmitted = nApplied * EF_N2O_DIRECT * N2O_N_TO_N2O // kg N2O
  // kg N2O * GWP / 1000 = tCO2e
  return +((n2oEmitted * GWP_N2O) / 1000).toFixed(3)
}

// --- MOBILE COMBUSTION ---
// Mobile emission factors now loaded from JSON
// Export for backward compatibility
export { mobileFuelFactors, mobileDistanceFactors, mobileFuelEmissionFactors } from "./emission-factors-loader"

/**
 * Calculate mobile emissions (tCO2e) for fuel-based method using DEFRA 2022 calorific values and densities.
 */
export function calculateMobileFuelEmissions(fuelType: string, amount: number, unit: string): number {
  const props = getFuelProperties(fuelType.toUpperCase())
  if (!props) {
    console.warn(`No fuel properties found for: ${fuelType}`)
    return 0
  }

  let kWh = 0,
    kg = 0
  switch (unit.toUpperCase()) {
    case "KWH":
      kWh = amount
      break
    case "GJ":
      kWh = amount * 277.78
      break
    case "KG":
      kg = amount
      kWh = props.netCV_kWh_per_kg ? amount * props.netCV_kWh_per_kg : 0
      break
    case "TONNES":
      kg = amount * 1000
      kWh = props.netCV_kWh_per_kg ? amount * 1000 * props.netCV_kWh_per_kg : 0
      break
    case "LITRES":
      // CNG is not valid in litres
      if (fuelType.toUpperCase() === "CNG") return 0
      if (props.density_kg_per_m3) {
        kg = (amount / 1000) * props.density_kg_per_m3
        kWh = props.netCV_kWh_per_litre ? amount * props.netCV_kWh_per_litre : 0
      }
      break
    case "M3":
      if (props.density_kg_per_m3) {
        kg = amount * props.density_kg_per_m3
        kWh = props.netCV_kWh_per_kg ? kg * props.netCV_kWh_per_kg : 0
      }
      break
    default:
      return 0
  }
  // Use the correct emission factor for each mobile fuel
  const fuelKey = fuelType.toUpperCase()
  const emissionFactorPerKWh = getMobileFuelEmissionFactor(fuelKey)
  const emission = kWh * emissionFactorPerKWh
  return +(emission / 1000).toFixed(3)
}

/**
 * Calculate mobile emissions (tCO2e) for distance-based method
 */
export function calculateMobileDistanceEmissions(category: string, distance: number, unit: string): number {
  // Map category to factor set
  let key = "CAR"
  if (/VAN|LCV/i.test(category)) key = "VAN"
  else if (/HGV|TRUCK/i.test(category)) key = "HGV"
  else if (/BUS|COACH/i.test(category)) key = "BUS"

  // Normalize unit to uppercase for consistent matching
  const normalizedUnit = unit.toUpperCase()
  const factor = getMobileDistanceFactor(key, normalizedUnit)
  if (factor === 0) {
    console.warn(`No distance factor found for category: ${category}, unit: ${unit}`)
    return 0
  }
  return +((distance * factor) / 1000).toFixed(3)
}

/**
 * Calculate mobile emissions (tCO2e) for expense-based method
 */
export function calculateMobileExpenseEmissions(expense: number): number {
  return +((expense * expenseFactorEUR) / 1000).toFixed(3)
}

// --- FUGITIVE EMISSIONS ---
// GWP values now loaded from JSON
// Export for backward compatibility
export { fugitiveGWP } from "./emission-factors-loader"

// Updated calorific values and densities now loaded from JSON
// Export for backward compatibility
export { fuelProperties } from "./emission-factors-loader"

// Standard emission factors for Scope 2 electricity and district energy now loaded from JSON
// Export for backward compatibility
export { scope2ElectricityEmissionFactors, scope2DistrictEnergyConfig } from "./emission-factors-loader"

// Shared utility: Convert any energy unit to kWh
export function toKWh(amount: number, unit: string): number {
  // Normalize to uppercase for consistent matching
  const normalizedUnit = unit.toUpperCase()

  switch (normalizedUnit) {
    case "KWH":
      return amount
    case "MWH":
      return amount * 1000
    case "GWH":
      return amount * 1_000_000
    case "GJ":
      return amount * 277.78
    default:
      console.warn(`Unsupported energy unit for conversion: ${unit}`)
      return 0
  }
}

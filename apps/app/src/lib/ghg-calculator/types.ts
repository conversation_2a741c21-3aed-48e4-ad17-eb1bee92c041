/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2025 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
export interface Field {
  id: string
  label: string
  type: "select" | "number" | "text" | "checkbox"
  options?: string[]
  unit_options?: string[]
}

export interface Section {
  id: string
  title: string
  fields: Field[]
}

export interface Schema {
  sections: Section[]
}

// Row types for forms
export type StationaryRow = {
  fuel: string
  consumption: string
  unit: string
  area: string
  areaUnit: string
}

export type FugitiveRow = {
  name: string
  amount?: string
  unit: string
}

export type MobileRow = {
  category: string
  consumption?: string
  unit?: string
  distance?: string
  distanceUnit?: string
  expense?: string
  fuelType?: string
}

export type AnimalRow = {
  animal: string
  count: string
  fieldPercentage: string
}

export type FertiliserRow = {
  type: string
  amount: string
  unit: string
}

export type ElectricityRow = {
  type: string
  consumption: string
  unit: string
  cost: string
  hasPPA: string
}

export type DistrictEnergyRow = {
  category: string
  consumption: string
  unit: string
  area: string
  areaUnit: string
}

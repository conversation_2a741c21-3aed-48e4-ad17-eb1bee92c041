/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

export const formSchema = {
  sections: [
    {
      id: "scope1_stationary",
      title: "Scope 1: Stationary Combustion",
      description:
        "Emissions resulting from the use of fuel for heating buildings or for other purposes (e.g., in equipment such as a generator). Please complete the data for all types of fuel consumed in stationary sources in the selected year. If no actual data is available, you may estimate emissions by floor area.",
      units: ["KWH", "LITRES", "M3", "TONNES"],
      areaUnits: ["M2", "FT2"],
      biomassOptions: [
        { value: "Biomass - wood logs", category: "biomass" },
        { value: "Biomass - wood chips", category: "biomass" },
        { value: "Biomass - wood pellets", category: "biomass" },
        { value: "Biomass - grass/straw", category: "biomass" },
      ],
      fossilOptions: [
        { value: "Natural gas", category: "fossil" },
        { value: "Heating oil", category: "fossil" },
        { value: "Diesel", category: "fossil" },
        { value: "Coal (domestic)", category: "fossil" },
        { value: "Coal (industrial)", category: "fossil" },
        { value: "LPG", category: "fossil" },
        { value: "Propane", category: "fossil" },
      ],
      columns: [
        {
          id: "fuel",
          label: "Fuel",
          type: "select",
          options: [
            { value: "Natural gas", category: "fossil" },
            { value: "Heating oil", category: "fossil" },
            { value: "Diesel", category: "fossil" },
            { value: "Coal (domestic)", category: "fossil" },
            { value: "Coal (industrial)", category: "fossil" },
            { value: "LPG", category: "fossil" },
            { value: "Propane", category: "fossil" },
            { value: "Biomass - wood logs", category: "biomass" },
            { value: "Biomass - wood chips", category: "biomass" },
            { value: "Biomass - wood pellets", category: "biomass" },
            { value: "Biomass - grass/straw", category: "biomass" },
          ],
        },
        {
          id: "consumption",
          label: "Consumption",
          type: "number",
        },
        {
          id: "unit",
          label: "Unit",
          type: "select",
          options: ["KWH", "LITRES", "M3", "TONNES"],
        },
        {
          id: "area",
          label: "Estimation: Area",
          type: "number",
        },
        {
          id: "areaUnit",
          label: "Area Unit",
          type: "select",
          options: ["M2", "FT2"],
        },
      ],
    },
    {
      id: "scope1_mobile",
      title: "Scope 1: Mobile Combustion",
      description:
        "Emissions from company-owned vehicles. Choose actual fuel data or estimate via distance or expense.",
      // columns controls table columns (each entry = one column)
      columns: [
        {
          id: "category",
          label: "Category",
          type: "select",
          options: ["Passenger cars", "LCVs", "HGVs", "Buses and coaches", "Other"],
        },
        { id: "consumption", label: "Consumption", type: "number" },
        { id: "fuelType", label: "Fuel Type", type: "select", options: ["PETROL", "DIESEL", "LPG", "CNG"] },
        { id: "unit", label: "Unit", type: "select", options: ["LITRES", "KG", "M3", "KWH"] },
        { id: "distance", label: "Distance", type: "number" },
        { id: "distanceUnit", label: "Distance Unit", type: "select", options: ["KM", "MILES"] },
        { id: "expense", label: "Expense (EUR)", type: "number" },
      ],
    },
    {
      id: "scope1_fugitive",
      title: "Scope 1: Fugitive Emissions",
      description:
        "Fugitive emissions from refrigeration and air conditioning result from leakage and service over the operational life of the equipment and from disposal at the end of the useful life of the equipment. No estimation is allowed. Please enter the actual leaked or serviced amount (in kg).",
      // columns controls table columns (each entry = one column)
      columns: [
        {
          id: "name",
          label: "Gas",
          type: "select",
          options: ["R134a", "R404A", "R410A", "R32", "SF6", "CH4", "N2O", "CO2"],
        },
        { id: "amount", label: "Amount", type: "number" },
        { id: "unit", label: "Unit", type: "select", options: ["KG", "TONNES", "LBS"] },
      ],
    },
    {
      id: "agriculture",
      title: "Agricultural Emissions (BIOGENIC SOURCES)",
      description:
        "This section applies only if your company operates in the agricultural industry. It includes enteric fermentation, manure management, and fertiliser use.",
      // columns for enteric fermentation
      columns: [
        {
          id: "animal",
          label: "Animal",
          type: "select",
          options: ["Dairy Cattle", "Non-dairy cattle", "Sheep", "Swine", "Goats", "Horses", "Poultry", "Rabbit"],
        },
        { id: "count", label: "Count", type: "number" },
        { id: "fieldPercentage", label: "% of manure goes into the field?", type: "number" },
      ],
      fertiliserColumns: [
        {
          id: "type",
          label: "Fertiliser Type",
          type: "select",
          options: [
            "Inorganic Nitrogen fertilizers",
            "Organic Nitrogen fertilizers",
            "Limestone",
            "Dolomite",
            "Animal manure purposefully applied to soils",
          ],
        },
        { id: "amount", label: "Amount", type: "number" },
        { id: "unit", label: "Unit", type: "select", options: ["KG"] },
      ],
    },
    {
      id: "scope2_electricity",
      title: "Scope 2: Purchased Electricity",
      description:
        "Enter the amount of purchased or externally supplied electricity. If you do not have consumption data, you may enter the annual cost instead. For RES, indicate if you have a contract/PPA or guarantees of origin.",
      units: ["KWH", "MWH", "GWH", "GJ"],
      columns: [
        { id: "type", label: "Type", type: "select", options: ["non-RES", "RES"] },
        { id: "consumption", label: "Annual Consumption", type: "number" },
        { id: "unit", label: "Unit", type: "select", options: ["KWH", "MWH", "GWH", "GJ"] },
        { id: "cost", label: "Annual Cost (EUR)", type: "number" },
        { id: "hasPPA", label: "PPA/Guarantee?", type: "select", options: ["NO", "YES"] },
      ],
    },
    {
      id: "scope2_district",
      title: "Scope 2: Heating, Cooling & Process Energy (Steam)",
      description:
        "Enter the amount of externally purchased or supplied heating, cooling, or steam. If you do not have consumption data, you may estimate by area. Only include district energy, not local sources.",
      units: ["KWH", "MWH", "GJ"],
      areaUnits: ["M2", "FT2"],
      columns: [
        { id: "category", label: "Category", type: "select", options: ["Heating", "Cooling", "Steam"] },
        { id: "consumption", label: "Annual Consumption", type: "number" },
        { id: "unit", label: "Unit", type: "select", options: ["KWH", "MWH", "GJ"] },
        { id: "area", label: "Estimation by Area", type: "number" },
        { id: "areaUnit", label: "Area Unit", type: "select", options: ["M2", "FT2"] },
      ],
    },
  ],
}

# GHG Calculator: Developer Guidelines

## Overview

This GHG calculator is **schema-driven**:

- All form structure, select options, and units are defined in [`formSchema.ts`](./formSchema.ts).
- All calculation logic (emission factors, conversions, etc.) is in [`calc-logic.ts`](./calc-logic.ts).
- Row types for each form are in [`types.ts`](./types.ts).

**Most changes (adding fuels, gases, units, etc.) require only schema and logic updates—no UI code changes!**

---

## Common Tasks & What to Modify

### 1. **Add a New Fuel, Gas, Animal, or Select Option**

- **Modify:**
  - `formSchema.ts`: Add your new value to the relevant `options` array.
  - `calc-logic.ts`: Add emission factor, GWP, or properties for the new value.
- **No need to modify UI or types unless you add a new field.**

### 2. **Add a New Unit (e.g., new energy or mass unit)**

- **Modify:**
  - `formSchema.ts`: Add the new unit to the relevant `options` array.
  - `calc-logic.ts`: Update conversion logic (e.g., add a case to `toKWh`).
- **No need to modify UI or types unless you add a new field.**

### 3. **Add a New Field/Column to a Form**

- **Modify:**
  - `formSchema.ts`: Add a new column to the relevant section's `columns` array.
  - `types.ts`: Update the corresponding row type (e.g., `StationaryRow`) to include the new field.
  - (Optional) UI: If the new field needs special rendering or logic, update the relevant form component.

### 4. **Add a Completely New Section/Form**

- **Modify:**
  - `formSchema.ts`: Add a new section object.
  - Create a new form component (e.g., `NewSectionForm.tsx`).
  - Register the new form in the app (e.g., in the section list or router).
  - (Optional) `types.ts`: Add a new row type if needed.
  - (Optional) `calc-logic.ts`: Add new calculation logic if the section needs it.

### 5. **Add a New Calculation Method or Logic**

- **Modify:**
  - `calc-logic.ts`: Add new functions or update existing ones.
  - (Optional) UI: Update form components if the new logic requires new UI elements or flows.

---

## Step-by-Step Example: Adding a New Fuel Type

Suppose you want to add **"Biogas"** to the Stationary Combustion form:

1. **Update the Schema (`formSchema.ts`):**

   ```ts
   // In the scope1_stationary section:
   {
     id: "fuel",
     label: "Fuel",
     type: "select",
     options: [
       // ...existing fuels...
       { value: "Biogas", category: "biomass" }, // <-- NEW FUEL
     ],
   }
   ```

2. **Update Calculation Logic (`calc-logic.ts`):**

   ```ts
   stationaryEmissionFactors["Biogas"] = 0.1 // (example value)
   fuelProperties["Biogas"] = {
     netCV_GJ_per_tonne: 20, // example value
     density_kg_per_m3: 1.2, // example value
     // Add other properties as needed
   }
   ```

3. **(Optional) Update Types (`types.ts`):**

   - Only needed if you add a new field/column.

4. **Test in the UI:**
   - Add a row, select "Biogas", enter values, and verify the calculation.

---

## Best Practices

- **Never hardcode options in components.** All options must be in the schema.
- **Keep categories and naming consistent** (e.g., `"fossil"`, `"biomass"`).
- **If you add a new select field,** always provide an `options` array.
- **Test your change** by running the app and checking the relevant form.

---

## Troubleshooting

- If your new option always returns 0 or doesn't work:
  - Check that you added it to the correct factors/properties in `calc-logic.ts`.
  - Check for typos (names must match exactly between schema and logic).
  - Check if you need to update unit conversion logic.

---

## Summary

- **For most new options (fuels, gases, units):**  
  Update `formSchema.ts` and `calc-logic.ts`.
- **For new fields:**  
  Update `formSchema.ts`, `types.ts`, and maybe the UI.
- **For new sections or calculation methods:**  
  Add new files/components as needed.

**If in doubt, check this guide or ask a team member.**

/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

"use server"

import { isNextRouterError } from "next/dist/client/components/is-next-router-error"
import { getPortalUserByEmail } from "@impactly/domain/portal-users/utils/get-portal-user-by-email"
import { z } from "zod"

import { signIn } from "@kreios/auth"

const emailSchema = z.object({
  email: z.string().email(),
  callbackUrl: z.string().optional(),
})

type ActionResult = {
  success?: boolean
  error?: {
    type: "USER_NOT_FOUND" | "VALIDATION_ERROR" | "UNKNOWN_ERROR"
    message: string
  }
}

export async function checkUserAndSendLogin(_prevState: ActionResult, formData: FormData): Promise<ActionResult> {
  try {
    // Validate the form data
    const validatedFields = emailSchema.safeParse({
      email: formData.get("email"),
      callbackUrl: formData.get("callbackUrl"),
    })

    if (!validatedFields.success) {
      return {
        error: {
          type: "VALIDATION_ERROR",
          message: "Please enter a valid email address.",
        },
      }
    }

    const { email, callbackUrl } = validatedFields.data

    // Check if user exists in portal users
    const portalUser = await getPortalUserByEmail(email)

    if (!portalUser) {
      return {
        error: {
          type: "USER_NOT_FOUND",
          message: "Your email address is not registered in our system.",
        },
      }
    }

    // User exists, proceed with sending magic link
    await signIn("email", {
      email,
      redirectTo: callbackUrl ?? "/admin",
    })

    return { success: true }
  } catch (error) {
    // Re-throw if it's a Next.js router error (expected behavior)
    // Don't log this as an error since it's the normal redirect flow
    if (isNextRouterError(error)) {
      throw error
    }

    // Only log actual errors, not expected redirects
    console.error("Error in checkUserAndSendLogin:", error)

    return {
      error: {
        type: "UNKNOWN_ERROR",
        message: "An unexpected error occurred. Please try again.",
      },
    }
  }
}
